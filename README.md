# Frontend API Integration Documentation

## Overview

This document describes the frontend API integration with the new Flask backend. All API calls have been updated to use the new modular backend structure with improved error handling and authentication.

## API Configuration

### Base Configuration
The API configuration is centralized in `src/config/api.ts`:

```typescript
export const API_CONFIG = {
  BASE_URL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000',
  ENDPOINTS: {
    ANALYZE: '/api/analyze',
    SAVE_ENTRY: '/api/save-entry',
    EMOTION_TREND: '/api/emotion-trend',
    PICK_EMOJI: '/api/pick-emoji',
    PICK_EMOJI_BATCH: '/api/pick-emoji-batch',
    RUN_PIPELINE: '/api/run',
    EMPATHY: '/api/empathy',
    UPDATE_TAGS: '/api/update-tags',
    UPDATE_TRANSCRIPT: '/api/update-transcript',
    WHISPER: '/api/whisper',
    TEST_OPENAI: '/api/test-openai',
    TEST_TAGS: '/api/test-tags'
  }
}
```

### Environment Variables
Set the following environment variable to point to your Flask backend:

```bash
NEXT_PUBLIC_API_URL=http://localhost:5000
```

## API Service Layer

### Core API Service (`src/lib/api/apiService.ts`)

The main API service provides:
- **Centralized authentication** using Supabase tokens
- **Consistent error handling** across all endpoints
- **Type-safe responses** with TypeScript interfaces
- **Automatic retry logic** and network error handling

#### Key Features:
- **Authentication**: Automatically includes Bearer tokens for protected endpoints
- **Error Handling**: Standardized error responses with detailed error information
- **Logging**: Comprehensive logging for debugging
- **Type Safety**: Full TypeScript support with response type definitions

### API Functions

#### Authentication Required Endpoints:
```typescript
// Tag analysis
analyzeTags(transcript: string, entryId?: string): Promise<ApiResponse<TagAnalysisResponse>>

// Save voice entries
saveEntry(payload: SaveEntryPayload): Promise<ApiResponse<SaveEntryResponse>>

// Emotion trends
getEmotionTrend(): Promise<ApiResponse<EmotionTrendResponse>>

// Emoji generation
pickEmoji(entryId: string, transcript?: string): Promise<ApiResponse<EmojiResponse>>
pickEmojiBatch(limit?: number): Promise<ApiResponse<any>>

// Update entries
updateTranscript(entryId: string, transcript_user: string): Promise<ApiResponse<any>>
updateTags(entryId: string, tags_user: string[]): Promise<ApiResponse<any>>
```

#### Public Endpoints:
```typescript
// Audio transcription
transcribeAudio(formData: FormData): Promise<ApiResponse<WhisperResponse>>

// Pipeline execution
runPipeline(text: string, userId?: string): Promise<ApiResponse<any>>

// Empathy processing
processEmpathy(transcript: string, userId?: string): Promise<ApiResponse<any>>

// Testing endpoints
testOpenAI(): Promise<ApiResponse<any>>
testTags(transcript?: string): Promise<ApiResponse<any>>
```

## Updated Components and Hooks

### Hooks Updated:

1. **useTagAnalysis** (`src/hooks/useTagAnalysis.ts`)
   - Now uses `analyzeTags` from API service
   - Improved error handling and response transformation
   - Maintains backward compatibility with existing interface

2. **useEmotionTrend** (`src/hooks/useEmotionTrend.ts`)
   - Updated to use `getEmotionTrend` from API service
   - Better error handling and loading states

3. **useFunkyEmoji** (`src/hooks/useFunkyEmoji.ts`)
   - Updated to use `pickEmoji` from API service
   - Maintains compatibility with useLazyCompute pattern

### Components Updated:

1. **RetroFridgeSection** (`src/components/RetroFridgeSection.tsx`)
   - Uses `pickEmojiBatch` for batch emoji generation
   - Improved error handling and user feedback

2. **CorkboardSection** (`src/components/CorkboardSection.tsx`)
   - Uses `pickEmojiBatch` for batch emoji generation
   - Better error handling and loading states

3. **RecordTab** (`src/components/tabs/RecordTab.tsx`)
   - Uses `processEmpathy` for empathy processing
   - Improved error handling and response processing

## Error Handling

### Error Handling Utilities (`src/lib/utils/errorHandling.ts`)

The frontend now includes comprehensive error handling:

```typescript
// Handle API errors
const error = handleApiError(apiError)

// Check error types
if (isNetworkError(error)) {
  // Handle network errors
}

if (isAuthError(error)) {
  // Handle authentication errors
}

// Get user-friendly error messages
const message = getErrorMessage(error)

// Log errors with context
logError('Component Name', error)
```

### Error Response Format

All API responses follow this format:

```typescript
interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  details?: string
}
```

## Authentication

### Token Management

The API service automatically handles authentication:

1. **Token Retrieval**: Gets Supabase session tokens automatically
2. **Header Injection**: Adds `Authorization: Bearer <token>` headers
3. **Error Handling**: Returns authentication errors for missing/invalid tokens

### Protected vs Public Endpoints

- **Protected Endpoints**: Require valid Supabase authentication token
- **Public Endpoints**: No authentication required (whisper, empathy, pipeline, tests)

## Migration Guide

### From Old API Calls to New Service

**Before:**
```typescript
const response = await fetch('/api/analyze', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ transcript, entryId })
})
const data = await response.json()
```

**After:**
```typescript
import { analyzeTags } from '@/lib/api/apiService'

const response = await analyzeTags(transcript, entryId)
if (response.success) {
  const data = response.data
  // Handle success
} else {
  // Handle error
  console.error(response.error)
}
```

### Benefits of New Approach

1. **Centralized Configuration**: All API endpoints configured in one place
2. **Automatic Authentication**: No need to manually handle tokens
3. **Better Error Handling**: Consistent error responses and handling
4. **Type Safety**: Full TypeScript support with response types
5. **Easier Testing**: Mockable service layer for testing
6. **Better Logging**: Comprehensive logging for debugging

## Testing

### API Service Testing

The API service can be easily mocked for testing:

```typescript
// Mock the API service
jest.mock('@/lib/api/apiService', () => ({
  analyzeTags: jest.fn(),
  saveEntry: jest.fn(),
  // ... other functions
}))
```

### Error Handling Testing

Test error scenarios:

```typescript
// Test network errors
const mockError = new Error('Network error')
analyzeTags.mockRejectedValue(mockError)

// Test authentication errors
const authError = { success: false, error: 'Authentication required' }
analyzeTags.mockResolvedValue(authError)
```

## Troubleshooting

### Common Issues

1. **CORS Errors**: Ensure Flask backend has CORS configured
2. **Authentication Errors**: Check Supabase session and token validity
3. **Network Errors**: Verify `NEXT_PUBLIC_API_URL` is correct
4. **Type Errors**: Ensure all API response types match expected interfaces

### Debug Mode

Enable debug logging by setting:

```typescript
// In your component
console.log('API Response:', response)
```

The API service includes comprehensive logging for debugging API calls.

## Future Enhancements

1. **Request/Response Interceptors**: Add middleware for request/response processing
2. **Retry Logic**: Implement automatic retry for failed requests
3. **Caching**: Add response caching for frequently accessed data
4. **Offline Support**: Implement offline mode with request queuing
5. **Real-time Updates**: Add WebSocket support for real-time features "# v1-Sentari-ui" 
