# Project Memory - User Preferences and Rules

## Technology Stack
- Next.js with TypeScript
- Supabase for backend/database
- Tailwind CSS for styling
- React hooks for state management
- Docker for consistent development environment

## Development Preferences
- TypeScript over JavaScript
- Environment variables for configuration
- Proper error handling and validation
- Clean code structure with proper separation of concerns

## Environment Setup
- Use .env.local for local development
- Proper Supabase project configuration required
- Environment variables must be validated before use
- Always provide helpful error messages with setup instructions
- Create documentation for common setup issues

## Code Style
- Use TypeScript strict mode
- Implement proper error boundaries
- Follow Next.js best practices
- Use semantic naming conventions 