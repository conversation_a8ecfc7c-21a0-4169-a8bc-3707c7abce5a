# Self Memory - Known Mistakes and Fixes

## Environment Variables

### Mistake: Missing Supabase Environment Variables
**Wrong**:
- Not setting up environment variables for Supabase client initialization
- Missing NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY
- Using non-null assertion (!) without validation
- Poor error messages that don't guide users to solution

**Correct**:
- Create .env.local file with proper Supabase credentials
- Validate environment variables before use instead of using non-null assertion
- Provide detailed error messages with setup instructions
- Add comprehensive validation in both server and client Supabase configurations
- Create setup documentation (SUPABASE_SETUP.md) for easy reference

## Next.js Configuration

### Mistake: Environment Variable Loading Issues
**Wrong**:
- Relying on process.env without proper Next.js configuration
- Not handling undefined environment variables gracefully
- Having incomplete environment variables across multiple .env files
- Not restarting development server after environment variable changes
- Inconsistent environment across different machines
- Docker builds failing due to missing devDependencies during build process

**Correct**:
- Use proper Next.js environment variable loading
- Add validation for required environment variables
- Implement fallback or error handling for missing variables
- Ensure all required variables are present in the appropriate .env file
- Always restart development server after environment variable changes
- Keep .env.local as the primary source for local development variables
- Use Docker for consistent environment across all machines
- Docker eliminates local environment variable loading issues
- Install ALL dependencies (including devDependencies) in Docker builds
- Create separate development and production Dockerfiles
- Use PowerShell scripts to properly load environment variables in Windows 