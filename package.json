{"name": "sentari-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 4000", "build": "next build", "start": "next start", "lint": "next lint", "test": "vitest run"}, "dependencies": {"@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.50.0", "@vercel/analytics": "^1.5.0", "chart.js": "^4.4.9", "clsx": "^2.1.1", "date-fns": "^4.1.0", "framer-motion": "^12.17.3", "franc": "^6.2.0", "lodash": "^4.17.21", "lucide-react": "^0.514.0", "next": "^15.3.3", "openai": "^5.3.0", "react": "^19.1.0", "react-chartjs-2": "^5.3.0", "react-dom": "^19.1.0", "tailwind-merge": "^3.3.1", "wink-tokenizer": "^5.3.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@jest/globals": "^30.0.2", "@tailwindcss/typography": "^0.5.16", "@types/jest": "^30.0.0", "@types/lodash": "^4.17.17", "@types/node": "^20.19.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.3.3", "jest": "^30.0.2", "postcss": "^8.4.35", "tailwindcss": "^3.4.17", "typescript": "^5.8.3", "vitest": "^1.5.0", "@types/wink-tokenizer": "^4.0.6", "tsx": "^4.7.0"}}