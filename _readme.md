当然，这是一个**非常高层级、结构清晰的总结**，告诉你所有这些资料和流程该怎么搭配使用。

---

## 🧭 Sentari 项目总协作指南（超高层级版本）

---

### 🧩 一、项目文件分类与功能

| 文件分类       | 文件名举例                                               | 用来干什么                                         |
| ---------- | --------------------------------------------------- | --------------------------------------------- |
| ✅ 核心协议类    | `AGREEMENTS_FULL.md`                                | 所有逻辑、功能、行为的硬性规则。任何人写代码必须遵守。                   |
| 🎨 UI规范类   | `UI_GUIDE.md`, `design_tokens.ts`                   | 确保界面风格一致、颜色统一，所有 UI 模块开发必须参考                  |
| 📘 功能说明类   | `EXECUTION_PLAN.md`                                 | 每个功能模块都写清楚目标（Purpose）、输入、处理、输出。是你启动每个任务的“配方卡” |
| 🧱 配置入门类   | `SYSTEM_README.md`, `.env.local.example`            | 新人或 AI 工具快速理解项目结构和配置方式的入口                     |
| 🧠 AI 提示语类 | `CURSOR_PROMPT.md`, `Sentari_Task_Start_Prompt.txt` | 每次用 ChatGPT 或 Cursor 开发功能时的标准提示语模板            |
| 🕒 日志追踪类   | `PROGRESS_LOG.md`                                   | 记录每天完成了什么、改了哪些文件、引用了哪些规范。避免混乱和重复开发            |

---

### 🔁 二、每日标准流程（3步循环）

| 步骤           | 行动                                                                             | 使用文件 |
| ------------ | ------------------------------------------------------------------------------ | ---- |
| **1️⃣ 启动模块** | 从 `EXECUTION_PLAN.md` 复制模块说明（P/I/P/O）贴入 `Sentari_Task_Start_Prompt.txt` 中启动任务  | ✅    |
| **2️⃣ 开发实现** | 在 ChatGPT / Cursor 中输入 prompt，同时引用 `AGREEMENTS_FULL.md` 和 `UI_GUIDE.md` 中的必要部分 | ✅    |
| **3️⃣ 记录进度** | 完成后，用 ChatGPT 生成一段 `PROGRESS_LOG.md` 条目并追加保存                                   | ✅    |

---

### 🧩 三、你只要记住这 4 句话，就能每天高效推进：

1. **要开发啥？去 `EXECUTION_PLAN.md` 找模块结构**
2. **怎么做？看 `AGREEMENTS_FULL.md` 和 `UI_GUIDE.md` 的约束**
3. **怎么开工？复制 prompt 用 `Sentari_Task_Start_Prompt.txt` 启动任务**
4. **做完记录？用 `PROGRESS_LOG.md` 保存今天完成什么**

---

### 🪄 附加建议

* 📅 每天创建一个新任务卡，使用 `Sentari_Daily_Task_Card_Template.md`
* ✅ 每完成一个模块，就部署一版，快速测试迭代（推荐用 Vercel）
* 🧠 所有 AI 工具都按“先读协议，再写代码”的逻辑协作

---

如果你希望我把这些再整理成一个 Notion 模板、PDF 或视觉版流程图，我也可以继续生成。是否需要？
