# Supabase Environment Setup Guide

## 🚨 Current Issue
Your application is failing because Supabase environment variables are not configured. This guide will help you set them up.

## 📋 Prerequisites
1. A Supabase project (create one at https://supabase.com)
2. Access to your Supabase project dashboard

## 🔧 Setup Steps

### Step 1: Get Your Supabase Credentials
1. Go to your Supabase project dashboard: https://supabase.com/dashboard
2. Select your project
3. Navigate to **Settings** → **API**
4. Copy the following values:
   - **Project URL** (looks like: `https://your-project-id.supabase.co`)
   - **anon public** key (starts with `eyJ...`)

### Step 2: Create Environment File
1. In your project root directory, create a file named `.env.local`
2. Add the following content (replace with your actual values):

```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### Step 3: Restart Your Development Server
After creating the `.env.local` file, restart your Next.js development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
```

## ✅ Verification
Once configured, you should see:
- No more Supabase environment variable errors
- Successful connection to your Supabase project
- Application running without 500 errors

## 🔒 Security Notes
- The `.env.local` file is already in your `.gitignore` and won't be committed
- These are public keys safe for client-side use
- Never commit your service role key (keep it server-side only)

## 🆘 Troubleshooting
If you still see errors:
1. Make sure the `.env.local` file is in the project root (same level as `package.json`)
2. Verify the URL format: `https://your-project-id.supabase.co`
3. Verify the anon key starts with `eyJ`
4. Restart your development server completely
5. Check the browser console for detailed error messages

## 📚 Additional Resources
- [Supabase Documentation](https://supabase.com/docs)
- [Next.js Environment Variables](https://nextjs.org/docs/basic-features/environment-variables)
- [Supabase Dashboard](https://supabase.com/dashboard) 