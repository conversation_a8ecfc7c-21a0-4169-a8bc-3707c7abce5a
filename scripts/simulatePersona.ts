import { processTranscript } from "../src/core/orchestrator";
import { randomUUID } from "crypto";

// A lightweight simulation of 30 diary-style entries for a fresh user profile.
// Run with:  npx tsx scripts/simulatePersona.ts

async function main() {
  const userId = `persona_${Date.now()}`;

  // Synthetic diary inputs for a young creative balancing work & life.
  const entries: string[] = [
    "First day at the new studio – feeling a mix of nerves and excitement.",
    "Spilled paint everywhere but somehow the chaos looked kinda cool.",
    "Had trouble focusing after lunch; my wrists ache from yesterday's sketches.",
    "Morning tai-chi felt grounding, maybe I should do it daily.",
    "Client loved the lotus series thumbnail! Proud but also anxious about final delivery.",
    "Ugh, Slack notifications kept popping – couldn't sink into deep work.",
    "Tried a bold stroke across the canvas; it either ruined or perfected it… not sure yet.",
    "Basketball with friends tonight helped clear my head.",
    "Stuck choosing the right brush size for delicate petals.",
    "Pulled a late one; joint pain flaring in my knees again.",
    "Quick coffee break turned into an hour of doom-scrolling.",
    "Painted a tiny water-lily detail I genuinely love.",
    "Energy is high today! Finished three thumbnails before noon!",
    "Feeling a bit burnt – load score creeping I bet.",
    "Tai-chi session at dusk calmed the storm in my head.",
    "Asked mentor for feedback; got validation and new ideas.",
    "Couldn't land the right color harmony; canvas feels muddy.",
    "Tried sketching outdoors, the breeze shifted everything in a good way.",
    "Back pain reminded me to stretch more.",
    "Thought of adding koi fish next to the lotus, maybe too much?",
    "Bold stroke experiment actually grew on me overnight.",
    "Basketball fast break moment replaying in my mind – movement inspiration!",
    "Took a midday nap, woke up refreshed and kind of proud of self-care.",
    "Team meeting tugged me away from flow, but ideas bubbled during it.",
    "Watered down paints accidentally created a dreamy effect.",
    "Knees stiff again; tai-chi tomorrow is a must.",
    "Celebrated small win with matcha latte instead of more coffee.",
    "Overwhelmed by upcoming deadline but sketch progress looks solid.",
    "Finished the final lotus panel – relief washing over me.",
    "Grateful for the journey; thinking about next bold series already."  
  ];

  for (let i = 0; i < entries.length; i++) {
    const transcript = entries[i];
    const meta = {
      entryId: randomUUID(),
      timestamp: new Date(Date.now() - (entries.length - i) * 60 * 60 * 1000).toISOString(),
    };

    const result = await processTranscript({ userId, transcript, meta });

    // Pretty print
    console.log(`\nEntry #${i + 1}: ${transcript}`);
    console.log(`Reply → ${result.response_text}`);
    console.log("--- Key Logs ---");
    result.debug_log.slice(0, 6).forEach((l) => console.log(l)); // first few log lines
  }
}

main().catch((e) => {
  // eslint-disable-next-line no-console
  console.error(e);
  process.exit(1);
}); 