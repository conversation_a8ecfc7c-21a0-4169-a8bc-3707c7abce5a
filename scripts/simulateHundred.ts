import { clearDB, preloadEntries, EntryRecord } from '../src/db';
import { runPipeline } from '../src/pipeline';

function fakeEntry(i: number): EntryRecord {
  const raw = `Fake past entry #${i}`;
  return {
    id: `${Date.now()}-${i}`,
    userId: 'demo',
    rawText: raw,
    createdAt: Date.now() - (100 - i) * 1000,
    parsed: {},
    meta: {},
    embedding: Array(32).fill(Math.random())
  };
}

async function main() {
  clearDB();
  const fakeEntries = Array.from({ length: 99 }, (_, i) => fakeEntry(i + 1));
  preloadEntries('demo', fakeEntries);
  const newTranscript = 'Hitting entry 100 — feeling both excited and a bit overwhelmed about how far I have come!';
  await runPipeline(newTranscript);
}

main(); 