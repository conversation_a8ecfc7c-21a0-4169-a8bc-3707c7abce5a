/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      fontFamily: {
        sans: ['Nunito', 'Inter', 'Quicksand', 'ui-sans-serif', 'system-ui'],
        chinese: ['"Source Han Sans"', '"思源黑体"', '"HarmonyOS Sans"', '"PingFang SC"', 'sans-serif']
      },
      colors: {
        tag: {
          'emotion-bg': '#FFF3E0',
          'emotion-text': '#F97316',
          'reflection-bg': '#EFF6FF',
          'reflection-text': '#3B82F6',
          'idea-bg': '#F0FDF4',
          'idea-text': '#22C55E'
        }
      },
      keyframes: {
        'fade-in': {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' }
        },
        ripple: {
          '0%': { transform: 'scale(1)', opacity: '0.6' },
          '100%': { transform: 'scale(2.2)', opacity: '0' }
        },
        'flow-x': {
          '0%, 100%': { transform: 'translateX(0%)' },
          '50%': { transform: 'translateX(-50%)' }
        },
        glow: {
          '0%, 100%': { transform: 'scale(1)', opacity: '0.45' },
          '50%': { transform: 'scale(1.15)', opacity: '0.8' },
        },
      },
      animation: {
        'fade-in': 'fade-in 0.3s ease-in-out forwards',
        ripple: 'ripple 1.8s cubic-bezier(0.4, 0, 0.2, 1) infinite',
        'flow-x': 'flow-x 24s linear infinite',
        glow: 'glow 6s ease-in-out infinite',
      }
    },
  },
  plugins: [],
} 