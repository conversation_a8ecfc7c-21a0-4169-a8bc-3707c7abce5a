import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  reactStrictMode: true,
  eslint: {
    // Allow production builds to successfully complete even if
    // there are ESLint errors. This is useful for rapid iteration
    // and avoids blocking CI/CD (e.g., Vercel) deployments.
    ignoreDuringBuilds: true,
  },
  // Note: Port configuration in next.config.ts is limited
  // Use package.json scripts or environment variables instead
};

export default nextConfig;
