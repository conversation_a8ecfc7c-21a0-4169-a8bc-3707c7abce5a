import { describe, it, expect } from "vitest";
import { generateMacroInsight } from "../src/core/insightEngine";

describe("Insight Engine", () => {
  it("generates insight every 7 entries", () => {
    const profile: any = { history: [], lastInsightCount: 0 };
    for (let i = 0; i < 7; i++) {
      profile.history.push({ theme: "growth", emotion: i < 3 ? "anxious" : "calm" });
    }
    const insight = generateMacroInsight(profile as any);
    expect(insight).toMatch(/growth/);
    expect(profile.lastInsightCount).toBe(7);
  });
}); 