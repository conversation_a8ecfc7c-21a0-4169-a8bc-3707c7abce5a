/// <reference types="vitest" />
import { describe, it, expect, afterEach, vi } from 'vitest';
import { log, getLogs, clearLogs } from '../src/logger';
import { LogTag } from '../src/types';

// Silence console during test
const spy = vi.spyOn(console, 'log').mockImplementation(() => {});

afterEach(() => {
  clearLogs();
  spy.mockClear();
});

describe('logger utility', () => {
  it('logs lines in the correct internal structure', () => {
    const input = 'hello';
    const output = 'world';
    log(LogTag.RAW_TEXT_IN, input, output, 'note');
    const logs = getLogs();

    expect(logs.length).toBe(1);
    expect(logs[0]).toEqual({
      tag: LogTag.RAW_TEXT_IN,
      input,
      output,
      note: 'note'
    });
  });
}); 