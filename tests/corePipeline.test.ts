import { describe, it, expect } from "vitest";
import { processTranscript } from "../src/core/orchestrator";

it("should generate a short empathy reply", async () => {
  const result = await processTranscript({
    userId: "demo",
    transcript: "Honestly I'm kinda proud—finishing that sketch felt amazing!",
  });
  expect(result.response_text.length).toBeLessThanOrEqual(150);
  expect(result.response_text).toMatch(/proud|burst|sketch/i);
}); 