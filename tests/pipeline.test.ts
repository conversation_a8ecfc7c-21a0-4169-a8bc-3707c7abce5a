import { describe, it, expect, afterEach, vi } from 'vitest';
import { runPipeline } from '../src/pipeline';
import { getLogs, clearLogs } from '../src/logger';
import { LogTag } from '../src/types';

vi.spyOn(console, 'log').mockImplementation(() => {});

afterEach(() => {
  clearLogs();
});

describe('pipeline skeleton', () => {
  it('produces 13 ordered log tags', async () => {
    await runPipeline('test entry');
    const tags = getLogs().map(l => l.tag);
    expect(tags).toEqual([
      LogTag.RAW_TEXT_IN,
      LogTag.EMBEDDING,
      LogTag.FETCH_RECENT,
      LogTag.FETCH_PROFILE,
      LogTag.META_EXTRACT,
      LogTag.PARSE_ENTRY,
      LogTag.CARRY_IN,
      LogTag.CONTRAST_CHECK,
      LogTag.PROFILE_UPDATE,
      LogTag.SAVE_ENTRY,
      LogTag.GPT_REPLY,
      LogTag.PUBLISH,
      LogTag.COST_LATENCY_LOG
    ]);
  });
}); 