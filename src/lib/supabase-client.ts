import { createBrowserClient } from '@supabase/ssr'

// ✅ SSR Optimized: Browser client for client components (singleton pattern)
let browserClient: ReturnType<typeof createBrowserClient> | null = null

export function createSupabaseBrowserClient() {
 
  // Validate environment variables
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

  if (!supabaseUrl || !supabaseAnonKey) {
    const errorMessage = `
🚨 Supabase Environment Variables Missing!

Required environment variables:
- NEXT_PUBLIC_SUPABASE_URL: ${supabaseUrl ? '✅ SET' : '❌ MISSING'}
- NEXT_PUBLIC_SUPABASE_ANON_KEY: ${supabaseAnonKey ? '✅ SET' : '❌ MISSING'}

To fix this:
1. Create a .env.local file in your project root
2. Add your Supabase credentials:
   NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key_here

Get these values from: https://supabase.com/dashboard/project/_/settings/api
    `
    console.error(errorMessage)
    
    // During build time, don't throw an error - return a mock client
    if (typeof window === 'undefined') {
      console.warn('Build time: Using mock Supabase client')
      return {
        auth: {
          getSession: async () => ({ data: { session: null }, error: null }),
          getUser: async () => ({ data: { user: null }, error: null }),
          exchangeCodeForSession: async () => ({ data: { session: null }, error: null })
        },
        from: () => ({
          select: () => ({ data: [], error: null }),
          insert: () => ({ data: [], error: null }),
          update: () => ({ data: [], error: null }),
          delete: () => ({ data: [], error: null })
        })
      } as any
    }
    
    throw new Error('Supabase environment variables are not configured. Please check the console for setup instructions.')
  }

  if (!browserClient) {
    browserClient = createBrowserClient(
      supabaseUrl,
      supabaseAnonKey
    )
  }
  return browserClient
} 