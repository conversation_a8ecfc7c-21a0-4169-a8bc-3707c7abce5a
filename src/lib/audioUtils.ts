export async function isSilentAudio(blob: Blob, threshold = 0.005): Promise<boolean> {
  const AudioCtx = (window.AudioContext || (window as any).webkitAudioContext) as typeof AudioContext | undefined
  if (!AudioCtx) return false

  const audioCtx = new AudioCtx()
  try {
    const arrayBuffer = await blob.arrayBuffer()
    const audioBuffer = await audioCtx.decodeAudioData(arrayBuffer)

    if (audioBuffer.numberOfChannels === 0) return true

    const data = audioBuffer.getChannelData(0)
    let sum = 0
    for (let i = 0; i < data.length; i++) sum += data[i] * data[i]
    const rms = Math.sqrt(sum / data.length)
    return rms < threshold
  } catch (err) {
    console.error('Audio silence detection failed:', err)
    return false
  } finally {
    audioCtx.close()
  }
} 