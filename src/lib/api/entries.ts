import { 
  transcribeAudio as apiTranscribe<PERSON>udio,
  saveEntry as apiSaveEntry,
  analyzeTags as apiAnalyzeTags,
  updateTranscript as apiUpdateTranscript,
  updateTags as apiUpdateTags,
  ApiResponse
} from './apiService'

export interface SaveEntryPayload {
  transcript_raw: string
  transcript_user: string
  language_detected: string
  language_rendered: string
  tags_model: string[]
  tags_user: string[]
  category?: string
  audio_duration?: number
  client_timestamp?: string
}

export async function transcribeAudio(formData: FormData) {
  const response = await apiTranscribeAudio(formData)
  
  if (!response.success) {
    throw new Error(response.error || 'Transcription failed')
  }
  
  return response.data
}

export async function saveEntry(payload: SaveEntryPayload) {
  const response = await apiSaveEntry(payload)
  
  if (!response.success) {
    throw new Error(response.error || 'Failed to save entry')
  }
  
  return response.data
}

export async function analyzeTags(transcript: string, entryId?: string) {
  const response = await apiAnalyzeTags(transcript, entryId)
  
  if (!response.success) {
    throw new Error(response.error || 'Tag analysis failed')
  }
  
  return response.data
}

export async function updateTranscript(entryId: string, transcript_user: string) {
  const response = await apiUpdateTranscript(entryId, transcript_user)
  
  if (!response.success) {
    throw new Error(response.error || 'Failed to update transcript')
  }
  
  return response.data
}

export async function updateTags(entryId: string, tags_user: string[]) {
  const response = await apiUpdateTags(entryId, tags_user)
  
  if (!response.success) {
    throw new Error(response.error || 'Failed to update tags')
  }
  
  return response.data
} 