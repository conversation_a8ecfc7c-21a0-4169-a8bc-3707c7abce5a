import { getApiUrl, getAuthHeaders, API_CONFIG } from '@/config/api'
import { supabaseAuthClient } from '@/lib/auth/authService'

// API Response types
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  details?: string
}

export interface TagAnalysisResponse {
  success: boolean
  analysis: {
    purpose: string
    tone: string
    category: string
    confidence: number
  }
  selectedTags: string[]
  timestamp: string
}

export interface SaveEntryResponse {
  success: boolean
  entry: any
}

export interface EmotionTrendResponse {
  trend: Array<{
    timestamp: string
    score: number
  }>
}

export interface EmojiResponse {
  success: boolean
  emoji: string
  source: string
  skipped?: boolean
}

export interface WhisperResponse {
  text: string
  language: string
  language_rendered: string
  duration: number
  segments: any[]
  enhanced: boolean
  debug: any
  transcriptionDetails: any
}

// Core Pipeline API types
export interface CorePipelineResponse {
  success: boolean
  response_text: string
  updated_profile: {
    user_id: string
    counters: {
      emotions: Record<string, number>
      themes: Record<string, number>
      buckets: Record<string, number>
    }
    load_score?: number
    last_updated: string
    history_count: number
    concepts?: string[]
  }
  debug_log: string[]
}

export interface CorePipelineRequest {
  transcript: string
  meta?: {
    device?: string
    silence_ms?: number
    entry_id?: string
  }
}

// Database API types
export interface Entry {
  id: string
  created_at: string
  transcript_user: string
  transcript_raw: string
  tags_user: string[]
  tags_model: string[]
  entry_emoji?: string
  embedding?: number[]
}

export interface EntryFeedItem {
  id: string
  created_at: string
  transcript_user: string
  tags_user: string[]
}

export interface EmojiEntry {
  id: string
  entry_emoji: string
  transcript_user: string
  created_at: string
}

export interface EmbeddingRecord {
  entry_id: string
  user_id: string
  text: string
  embedding: number[]
}

export interface ScoredMemory {
  record: EmbeddingRecord
  score: number
}

export interface UserProfile {
  userId: string
  [key: string]: any
}

// Helper function to get authentication token
async function getAuthToken(): Promise<string | null> {
  try {
    const { data } = await supabaseAuthClient.auth.getSession()
    return data.session?.access_token || null
  } catch (error) {
    console.error('Failed to get auth token:', error)
    return null
  }
}

// Generic API call function with authentication
async function apiCall<T>(
  endpoint: string,
  options: {
    method?: string
    body?: any
    requiresAuth?: boolean
    contentType?: string
  } = {}
): Promise<ApiResponse<T>> {
  const {
    method = 'GET',
    body,
    requiresAuth = true,
    contentType = 'application/json'
  } = options

  try {
    const url = getApiUrl(endpoint)
    const headers: Record<string, string> = {}

    // Add content type
    if (contentType !== 'multipart/form-data') {
      headers['Content-Type'] = contentType
    }

    // Add authentication if required
    if (requiresAuth) {
      const token = await getAuthToken()
      if (token) {
        headers['Authorization'] = `Bearer ${token}`
      } else {
        return {
          success: false,
          error: 'Authentication required',
          details: 'No valid session found'
        }
      }
    }

    const requestOptions: RequestInit = {
      method,
      headers
    }

    // Add body if provided
    if (body) {
      if (contentType === 'multipart/form-data') {
        requestOptions.body = body
      } else {
        requestOptions.body = JSON.stringify(body)
      }
    }

    console.log(`🌐 API Call: ${method} ${url}`, {
      requiresAuth,
      hasBody: !!body,
      contentType
    })

    const response = await fetch(url, requestOptions)
    const responseData = await response.json()

    if (!response.ok) {
      return {
        success: false,
        error: responseData.error || `HTTP ${response.status}`,
        details: responseData.details || response.statusText
      }
    }

    return {
      success: true,
      data: responseData.data || responseData
    }

  } catch (error) {
    console.error(`🟥 API call failed for ${endpoint}:`, error)
    return {
      success: false,
      error: 'Network error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

// API Functions

export async function transcribeAudio(formData: FormData): Promise<ApiResponse<WhisperResponse>> {
  return apiCall<WhisperResponse>(API_CONFIG.ENDPOINTS.WHISPER, {
    method: 'POST',
    body: formData,
    requiresAuth: false,
    contentType: 'multipart/form-data'
  })
}

export async function saveEntry(payload: {
  transcript_raw: string
  transcript_user: string
  language_detected: string
  language_rendered: string
  tags_model: string[]
  tags_user: string[]
  category?: string
  audio_duration?: number
  client_timestamp?: string
}): Promise<ApiResponse<SaveEntryResponse>> {
  return apiCall<SaveEntryResponse>(API_CONFIG.ENDPOINTS.SAVE_ENTRY, {
    method: 'POST',
    body: payload,
    requiresAuth: true
  })
}

export async function analyzeTags(
  transcript: string, 
  entryId?: string
): Promise<ApiResponse<TagAnalysisResponse>> {
  return apiCall<TagAnalysisResponse>(API_CONFIG.ENDPOINTS.ANALYZE, {
    method: 'POST',
    body: { transcript, entryId },
    requiresAuth: true
  })
}

export async function updateTranscript(
  entryId: string, 
  transcript_user: string
): Promise<ApiResponse<any>> {
  return apiCall(API_CONFIG.ENDPOINTS.UPDATE_TRANSCRIPT, {
    method: 'POST',
    body: { entryId, transcript_user },
    requiresAuth: true
  })
}

export async function updateTags(
  entryId: string, 
  tags_user: string[]
): Promise<ApiResponse<any>> {
  return apiCall(API_CONFIG.ENDPOINTS.UPDATE_TAGS, {
    method: 'POST',
    body: { entryId, tags_user },
    requiresAuth: true
  })
}

export async function getEmotionTrend(): Promise<ApiResponse<EmotionTrendResponse>> {
  return apiCall<EmotionTrendResponse>(API_CONFIG.ENDPOINTS.EMOTION_TREND, {
    method: 'POST',
    body: {},
    requiresAuth: true
  })
}

export async function pickEmoji(
  entryId: string, 
  transcript?: string
): Promise<ApiResponse<EmojiResponse>> {
  return apiCall<EmojiResponse>(API_CONFIG.ENDPOINTS.PICK_EMOJI, {
    method: 'POST',
    body: { entryId, transcript },
    requiresAuth: true
  })
}

export async function pickEmojiBatch(limit?: number): Promise<ApiResponse<any>> {
  return apiCall(API_CONFIG.ENDPOINTS.PICK_EMOJI_BATCH, {
    method: 'POST',
    body: { limit },
    requiresAuth: true
  })
}

export async function runPipeline(
  text: string, 
  userId?: string
): Promise<ApiResponse<any>> {
  return apiCall(API_CONFIG.ENDPOINTS.RUN_PIPELINE, {
    method: 'POST',
    body: { text, userId },
    requiresAuth: false
  })
}

export async function processEmpathy(
  transcript: string, 
  userId?: string
): Promise<ApiResponse<any>> {
  return apiCall(API_CONFIG.ENDPOINTS.EMPATHY, {
    method: 'POST',
    body: { transcript, userId },
    requiresAuth: false
  })
}

export async function testOpenAI(): Promise<ApiResponse<any>> {
  return apiCall(API_CONFIG.ENDPOINTS.TEST_OPENAI, {
    method: 'GET',
    requiresAuth: false
  })
}

export async function testTags(transcript?: string): Promise<ApiResponse<any>> {
  return apiCall(API_CONFIG.ENDPOINTS.TEST_TAGS, {
    method: transcript ? 'POST' : 'GET',
    body: transcript ? { transcript } : undefined,
    requiresAuth: false
  })
}

// Database API Functions

// Entries API
export async function getEntries(params?: {
  limit?: number
  offset?: number
  tags?: string[]
  start_date?: string
  end_date?: string
}): Promise<ApiResponse<Entry[]>> {
  const searchParams = new URLSearchParams()
  if (params?.limit) searchParams.append('limit', params.limit.toString())
  if (params?.offset) searchParams.append('offset', params.offset.toString())
  if (params?.start_date) searchParams.append('start_date', params.start_date)
  if (params?.end_date) searchParams.append('end_date', params.end_date)
  if (params?.tags) params.tags.forEach(tag => searchParams.append('tags', tag))
  
  const endpoint = `/api/entries${searchParams.toString() ? `?${searchParams.toString()}` : ''}`
  return apiCall<Entry[]>(endpoint, { method: 'GET', requiresAuth: true })
}

export async function getEntry(entryId: string): Promise<ApiResponse<Entry>> {
  return apiCall<Entry>(`/api/entries/${entryId}`, { method: 'GET', requiresAuth: true })
}

export async function searchEntries(query: string): Promise<ApiResponse<Entry[]>> {
  return apiCall<Entry[]>('/api/entries/search', {
    method: 'POST',
    body: { query },
    requiresAuth: true
  })
}

export async function deleteEntry(entryId: string): Promise<ApiResponse<any>> {
  return apiCall(`/api/entries/${entryId}`, { method: 'DELETE', requiresAuth: true })
}

export async function updateEntryTranscript(entryId: string, transcript: string): Promise<ApiResponse<Entry>> {
  return apiCall<Entry>(`/api/entries/${entryId}/transcript`, {
    method: 'PUT',
    body: { transcript },
    requiresAuth: true
  })
}

export async function updateEntryTags(entryId: string, tags: string[]): Promise<ApiResponse<Entry>> {
  return apiCall<Entry>(`/api/entries/${entryId}/tags`, {
    method: 'PUT',
    body: { tags },
    requiresAuth: true
  })
}

export async function updateEntryField(entryId: string, field: string, value: any): Promise<ApiResponse<Entry>> {
  return apiCall<Entry>(`/api/entries/${entryId}/field`, {
    method: 'PUT',
    body: { field, value },
    requiresAuth: true
  })
}

export async function getRecentEmojiEntries(limit?: number): Promise<ApiResponse<EmojiEntry[]>> {
  const searchParams = new URLSearchParams()
  if (limit) searchParams.append('limit', limit.toString())
  
  const endpoint = `/api/entries/recent-emoji${searchParams.toString() ? `?${searchParams.toString()}` : ''}`
  return apiCall<EmojiEntry[]>(endpoint, { method: 'GET', requiresAuth: true })
}

export async function getAvailableTags(): Promise<ApiResponse<string[]>> {
  return apiCall<string[]>('/api/entries/tags', { method: 'GET', requiresAuth: true })
}

// Embeddings API
export async function upsertEmbedding(entryId: string, text: string, embedding: number[]): Promise<ApiResponse<any>> {
  return apiCall('/api/embeddings', {
    method: 'POST',
    body: { entry_id: entryId, text, embedding },
    requiresAuth: true
  })
}

export async function searchSimilarEmbeddings(
  queryEmbedding: number[],
  matchThreshold?: number,
  matchCount?: number
): Promise<ApiResponse<ScoredMemory[]>> {
  return apiCall<ScoredMemory[]>('/api/embeddings/search', {
    method: 'POST',
    body: {
      query_embedding: queryEmbedding,
      match_threshold: matchThreshold,
      match_count: matchCount
    },
    requiresAuth: true
  })
}

export async function getEmbedding(entryId: string): Promise<ApiResponse<EmbeddingRecord>> {
  return apiCall<EmbeddingRecord>(`/api/embeddings/${entryId}`, { method: 'GET', requiresAuth: true })
}

export async function deleteEmbedding(entryId: string): Promise<ApiResponse<any>> {
  return apiCall(`/api/embeddings/${entryId}`, { method: 'DELETE', requiresAuth: true })
}

export async function getUserEmbeddings(limit?: number): Promise<ApiResponse<EmbeddingRecord[]>> {
  const searchParams = new URLSearchParams()
  if (limit) searchParams.append('limit', limit.toString())
  
  const endpoint = `/api/embeddings${searchParams.toString() ? `?${searchParams.toString()}` : ''}`
  return apiCall<EmbeddingRecord[]>(endpoint, { method: 'GET', requiresAuth: true })
}

// Profiles API
export async function getProfile(): Promise<ApiResponse<UserProfile>> {
  return apiCall<UserProfile>('/api/profiles', { method: 'GET', requiresAuth: true })
}

export async function upsertProfile(profile: UserProfile, concepts?: any): Promise<ApiResponse<any>> {
  return apiCall('/api/profiles', {
    method: 'POST',
    body: { profile, concepts },
    requiresAuth: true
  })
}

export async function updateProfileField(field: string, value: any): Promise<ApiResponse<any>> {
  return apiCall('/api/profiles/field', {
    method: 'PUT',
    body: { field, value },
    requiresAuth: true
  })
}

export async function deleteProfile(): Promise<ApiResponse<any>> {
  return apiCall('/api/profiles', { method: 'DELETE', requiresAuth: true })
}

export async function getProfileConcepts(): Promise<ApiResponse<any>> {
  return apiCall('/api/profiles/concepts', { method: 'GET', requiresAuth: true })
}

export async function updateProfileConcepts(concepts: any): Promise<ApiResponse<any>> {
  return apiCall('/api/profiles/concepts', {
    method: 'PUT',
    body: { concepts },
    requiresAuth: true
  })
}

// Tags API
export async function getAllTags(): Promise<ApiResponse<string[]>> {
  return apiCall<string[]>('/api/tags', { method: 'GET', requiresAuth: true })
}

export async function getEntriesByTag(tag: string): Promise<ApiResponse<Entry[]>> {
  return apiCall<Entry[]>(`/api/tags/${encodeURIComponent(tag)}`, { method: 'GET', requiresAuth: true })
}

export async function getEntriesByTags(tags: string[]): Promise<ApiResponse<Entry[]>> {
  return apiCall<Entry[]>('/api/tags/search', {
    method: 'POST',
    body: { tags },
    requiresAuth: true
  })
}

export async function getTagUsageCount(): Promise<ApiResponse<Record<string, number>>> {
  return apiCall<Record<string, number>>('/api/tags/usage', { method: 'GET', requiresAuth: true })
}

export async function getPopularTags(limit?: number): Promise<ApiResponse<Array<{tag: string, count: number}>>> {
  const searchParams = new URLSearchParams()
  if (limit) searchParams.append('limit', limit.toString())
  
  const endpoint = `/api/tags/popular${searchParams.toString() ? `?${searchParams.toString()}` : ''}`
  return apiCall<Array<{tag: string, count: number}>>(endpoint, { method: 'GET', requiresAuth: true })
}

// Core Pipeline API
export async function processTranscriptCore(
  request: CorePipelineRequest
): Promise<ApiResponse<CorePipelineResponse>> {
  return apiCall<CorePipelineResponse>('/api/core/process-transcript', {
    method: 'POST',
    body: request,
    requiresAuth: true
  })
}

export async function checkCoreHealth(): Promise<ApiResponse<{core_available: boolean, status: string}>> {
  return apiCall<{core_available: boolean, status: string}>('/api/core/health', {
    method: 'GET',
    requiresAuth: false
  })
} 