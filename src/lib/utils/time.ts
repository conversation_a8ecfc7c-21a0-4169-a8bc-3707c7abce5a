export function getLocalISOStringWithOffset() {
  const date = new Date()
  const offsetMinutes = -date.getTimezoneOffset() // positive east of UTC
  const sign = offsetMinutes >= 0 ? '+' : '-'
  const absMinutes = Math.abs(offsetMinutes)
  const hours = Math.floor(absMinutes / 60)
  const minutes = absMinutes % 60

  // Local date adjusted
  const localMs = date.getTime() + date.getTimezoneOffset() * 60 * 1000
  const localDate = new Date(localMs)

  const isoWithoutZ = localDate.toISOString().slice(0, -1) // drop trailing Z
  const tz = `${sign}${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`
  return isoWithoutZ + tz
} 