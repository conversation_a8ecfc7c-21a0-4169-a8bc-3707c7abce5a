// Error handling utilities for the frontend

export interface ApiError {
  message: string
  details?: string
  code?: string
  status?: number
}

export function handleApiError(error: any): ApiError {
  if (error instanceof Error) {
    return {
      message: error.message,
      details: error.stack
    }
  }
  
  if (typeof error === 'string') {
    return {
      message: error
    }
  }
  
  if (error && typeof error === 'object') {
    return {
      message: error.message || error.error || 'Unknown error occurred',
      details: error.details || error.stack,
      code: error.code,
      status: error.status
    }
  }
  
  return {
    message: 'An unexpected error occurred'
  }
}

export function isNetworkError(error: any): boolean {
  return error?.message?.includes('Network error') || 
         error?.message?.includes('fetch') ||
         error?.message?.includes('Failed to fetch')
}

export function isAuthError(error: any): boolean {
  return error?.message?.includes('Authentication required') ||
         error?.message?.includes('Unauthorized') ||
         error?.status === 401
}

export function getErrorMessage(error: any): string {
  const apiError = handleApiError(error)
  
  if (isNetworkError(error)) {
    return 'Connection error. Please check your internet connection and try again.'
  }
  
  if (isAuthError(error)) {
    return 'Please log in to continue.'
  }
  
  return apiError.message
}

export function logError(context: string, error: any): void {
  const apiError = handleApiError(error)
  console.error(`🟥 ${context}:`, {
    message: apiError.message,
    details: apiError.details,
    code: apiError.code,
    status: apiError.status
  })
}

// Toast notification helper (if you have a toast system)
export function showErrorToast(error: any): void {
  const message = getErrorMessage(error)
  // If you have a toast system, call it here
  // toast.error(message)
  console.error('Error toast:', message)
}

export function showSuccessToast(message: string): void {
  // If you have a toast system, call it here
  // toast.success(message)
  console.log('Success toast:', message)
} 