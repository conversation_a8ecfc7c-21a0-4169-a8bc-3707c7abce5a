export function getLocalTimestamp(): string {
  const d = new Date()

  const pad = (n: number, len = 2) => n.toString().padStart(len, '0')
  const yyyy = d.getFullYear()
  const mm = pad(d.getMonth() + 1)
  const dd = pad(d.getDate())
  const hh = pad(d.getHours())
  const mi = pad(d.getMinutes())
  const ss = pad(d.getSeconds())
  const ms = pad(d.getMilliseconds(), 3)

  return `${yyyy}-${mm}-${dd} ${hh}:${mi}:${ss}.${ms}`
} 