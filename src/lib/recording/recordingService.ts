export class RecordingService {
  private mediaRecorder: MediaRecorder | null = null
  private audioChunks: Blob[] = []
  private recordingStartTime = 0

  async start(): Promise<void> {
    if (this.mediaRecorder && this.mediaRecorder.state === 'recording') {
      // already recording
      return
    }

    const stream = await navigator.mediaDevices.getUserMedia({
      audio: {
        echoCancellation: true,
        noiseSuppression: true,
        autoGainControl: true,
        sampleRate: 16000,
        channelCount: 1,
      },
    })

    const candidates = [
      'audio/webm;codecs=opus',            // Chrome / Edge
      'audio/mp4;codecs=mp4a.40.2',        // iOS Safari AAC
      'audio/mp4',                         // generic mp4
      'audio/aac',                         // some Safari versions
      ''                                   // let browser choose
    ]

    const selectedMime = candidates.find(c => c && MediaRecorder.isTypeSupported(c)) || ''

    const options = {
      mimeType: selectedMime || undefined, // if empty, let browser choose
      audioBitsPerSecond: 16000,
    } as MediaRecorderOptions

    this.mediaRecorder = new MediaRecorder(stream, options)
    this.audioChunks = []
    this.recordingStartTime = Date.now()

    this.mediaRecorder.ondataavailable = (event: BlobEvent) => {
      if (event.data.size > 0) {
        this.audioChunks.push(event.data)
      }
    }

    this.mediaRecorder.start(1000) // collect every second
  }

  async stop(): Promise<{ blob: Blob; duration: number }> {
    if (!this.mediaRecorder) throw new Error('No active recording')

    return new Promise((resolve) => {
      const recorder = this.mediaRecorder as MediaRecorder

      recorder.onstop = () => {
        const blob = new Blob(this.audioChunks, { type: 'audio/webm' })
        const duration = Math.round((Date.now() - this.recordingStartTime) / 1000)

        // clean up tracks
        recorder.stream.getTracks().forEach((track) => track.stop())
        this.mediaRecorder = null
        this.audioChunks = []

        resolve({ blob, duration })
      }

      recorder.stop()
    })
  }

  isRecording() {
    return this.mediaRecorder?.state === 'recording'
  }
}

export const recordingService = new RecordingService() 