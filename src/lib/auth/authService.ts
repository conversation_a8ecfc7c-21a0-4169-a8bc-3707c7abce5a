import { createSupabaseBrowserClient } from '@/lib/supabase-client'

// Singleton supabase browser client for auth operations
export const supabaseAuthClient = createSupabaseBrowserClient()

export async function getSession() {
  const { data } = await supabaseAuthClient.auth.getSession()
  return data.session
}

export function signInWithGoogle() {
  return supabaseAuthClient.auth.signInWithOAuth({
    provider: 'google',
    options: {
      redirectTo: typeof window !== 'undefined' ? `${window.location.origin}/auth/callback` : undefined,
    },
  })
}

export function signOut() {
  return supabaseAuthClient.auth.signOut()
} 