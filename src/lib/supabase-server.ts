import { createServerClient } from '@supabase/ssr'
import { cookies, headers } from 'next/headers'
import { NextRequest } from 'next/server'
import { Session } from '@supabase/supabase-js'

// ✅ SSR Optimized: Server-side client for API routes and server components
export async function createSupabaseServerClient() {

  // Validate environment variables
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

  if (!supabaseUrl || !supabaseAnonKey) {
    const errorMessage = `
🚨 Supabase Environment Variables Missing!

Required environment variables:
- NEXT_PUBLIC_SUPABASE_URL: ${supabaseUrl ? '✅ SET' : '❌ MISSING'}
- NEXT_PUBLIC_SUPABASE_ANON_KEY: ${supabaseAnonKey ? '✅ SET' : '❌ MISSING'}

To fix this:
1. Create a .env.local file in your project root
2. Add your Supabase credentials:
   NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key_here

Get these values from: https://supabase.com/dashboard/project/_/settings/api
    `
    console.error(errorMessage)
    
    // During build time, don't throw an error - return a mock client
    if (process.env.NODE_ENV === 'development' || typeof window === 'undefined') {
      console.warn('Build time: Using mock Supabase server client')
      return {
        auth: {
          getSession: async () => ({ data: { session: null }, error: null }),
          getUser: async () => ({ data: { user: null }, error: null }),
          setSession: async () => ({ data: { session: null }, error: null })
        },
        from: () => ({
          select: () => ({ data: [], error: null }),
          insert: () => ({ data: [], error: null }),
          update: () => ({ data: [], error: null }),
          delete: () => ({ data: [], error: null })
        })
      } as any
    }
    
    throw new Error('Supabase environment variables are not configured. Please check the console for setup instructions.')
  }

  const cookieStore = await cookies()
  const headerList = await headers()
  const authHeader = headerList.get('authorization') || headerList.get('Authorization')
  const bearerToken = authHeader?.startsWith('Bearer ') ? authHeader.slice(7) : undefined

  return createServerClient(
    supabaseUrl,
    supabaseAnonKey,
    {
      global: bearerToken ? { headers: { Authorization: `Bearer ${bearerToken}` } } : undefined,
      cookies: {
        getAll() {
          // On Next 15 Route Handlers cookieStore is mutable; in Server Components it's read-only.
          // We guard with optional chaining so build doesn't crash.
          // @ts-ignore
          return cookieStore.getAll?.() ?? []
        },
        setAll(cookiesToSet) {
          cookiesToSet.forEach(({ name, value, options }) => {
            try {
              // @ts-ignore – set only exists in mutable cookieStore
              cookieStore.set?.(name, value, options)
            } catch {
              /* noop in server components */
            }
          })
        },
      },
    }
  )
}

// ✅ 推荐标准用法：在 SSR 或 API Route 里用
export async function getSession(context?: { req?: NextRequest }) {
  const supabase = await createSupabaseServerClient()
  const { data: { session }, error } = await supabase.auth.getSession()
  
  if (session) {
    return { session, error: null }
  }
  
  // Fallback: try Authorization header (e.g., from client fetch)
  const authHeader = context?.req?.headers.get('authorization') || context?.req?.headers.get('Authorization')
  if (authHeader?.startsWith('Bearer ')) {
    const token = authHeader.slice(7)
    const { data: userData, error: userError } = await supabase.auth.getUser(token)
    if (userData?.user) {
      // Attach the token so subsequent DB calls run as the user
      await supabase.auth.setSession({ access_token: token, refresh_token: '' })
      const fauxSession = { user: userData.user } as unknown as Session
      return { session: fauxSession, error: userError }
    }
  }
  
  if (error) {
    console.error('🟥 Session error:', error)
  }
  
  return { session: null, error }
} 