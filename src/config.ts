export const OPENAI_API_KEY = process.env.OPENAI_API_KEY || '';

// If USE_MOCK not set, default to true when no key is provided.
export const USE_MOCK = process.env.USE_MOCK
  ? process.env.USE_MOCK === 'true'
  : OPENAI_API_KEY.length === 0;

export const OPENAI_EMBED_MODEL = process.env.OPENAI_EMBED_MODEL || 'text-embedding-3-small';
export const OPENAI_CHAT_MODEL = process.env.OPENAI_CHAT_MODEL || 'gpt-3.5-turbo'; 