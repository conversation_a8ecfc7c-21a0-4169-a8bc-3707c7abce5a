export type Transcript = string;

export interface ParsedEntry {
  theme: string[];
  vibe: string[];
  intent: string;
  subtext: string;
  persona_trait: string[];
  bucket: string[];
}

export interface MetaData {
  wordCount: number;
  hasQuestion: boolean;
  hasExclaim: boolean;
  energy: number; // heuristic score 0–1
  topWords: string[];
}

export interface Profile {
  top_themes: string[];
  theme_count: Record<string, number>;
  dominant_vibe: string;
  vibe_count: Record<string, number>;
  bucket_count: Record<string, number>;
  trait_pool: string[];
  last_theme?: string;
}

export enum LogTag {
  RAW_TEXT_IN = "RAW_TEXT_IN",
  EMBEDDING = "EMBEDDING",
  FETCH_RECENT = "FETCH_RECENT",
  FETCH_PROFILE = "FETCH_PROFILE",
  META_EXTRACT = "META_EXTRACT",
  PARSE_ENTRY = "PARSE_ENTRY",
  CARRY_IN = "CARRY_IN",
  CONTRAST_CHECK = "CONTRAST_CHECK",
  PROFILE_UPDATE = "PROFILE_UPDATE",
  SAVE_ENTRY = "SAVE_ENTRY",
  GPT_REPLY = "GPT_REPLY",
  PUBLISH = "PUBLISH",
  COST_LATENCY_LOG = "COST_LATENCY_LOG"
}

export interface LogLine {
  tag: LogTag;
  input: any;
  output: any;
  note?: string;
}

export type EmbeddingVector = number[]; 