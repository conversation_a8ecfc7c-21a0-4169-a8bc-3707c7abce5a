import { LogLine, LogTag } from './types';

const logs: LogLine[] = [];

/**
 * Push a pipeline log line following the required format:
 * [TAG] input=<...> | output=<...> | note=<...>
 */
export function log(tag: LogTag, input: unknown, output: unknown, note?: string): void {
  const line: LogLine = { tag, input, output, note };
  logs.push(line);

  const printable: string[] = [
    `[${tag}]`,
    `input=${JSON.stringify(input)}`,
    `output=${JSON.stringify(output)}`
  ];
  if (note !== undefined) printable.push(`note=${note}`);
  // eslint-disable-next-line no-console
  console.log(printable.join(' | '));
}

export function getLogs(): LogLine[] {
  return [...logs];
}

export function clearLogs(): void {
  logs.length = 0;
} 