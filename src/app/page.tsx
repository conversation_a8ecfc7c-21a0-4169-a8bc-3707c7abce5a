'use client'

import { useTab } from '@/components/TabProvider'
import RecordTab from '@/components/tabs/RecordTab'
import HistoryTab from '@/components/tabs/HistoryTab'
import InsightTab from '@/components/tabs/InsightTab'

function AppContent() {
  const { activeTab } = useTab()

  const renderActiveTab = () => {
    switch (activeTab) {
      case 'record':
        return <RecordTab />
      case 'history':
        return <HistoryTab />
      case 'insight':
        return <InsightTab />
      default:
        return <RecordTab />
    }
  }

  return renderActiveTab()
}

export default function HomePage() {
  return <AppContent />
}
