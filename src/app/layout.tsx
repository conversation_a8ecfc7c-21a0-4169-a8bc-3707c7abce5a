import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import AuthProvider from '@/components/AuthProvider'
import { getSession } from '@/lib/supabase-server'
import { TabProvider } from '@/components/TabProvider'
import DevLayout from '@/components/DevLayout'
import { Analytics } from '@vercel/analytics/next'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'Sentari AI - Voice Journal',
  description: 'AI-powered voice recording and transcription',
  icons: {
    icon: [
      { url: '/favicon.ico' },
      { url: '/favicon-32x32.png', sizes: '32x32' },
      { url: '/favicon-16x16.png', sizes: '16x16' }
    ],
    apple: '/apple-touch-icon.png',
  },
}

export default async function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  // ✅ 推荐标准用法：在 SSR 里用
  const { session } = await getSession()

  return (
    <html lang="en">
      <body className={inter.className}>
        <AuthProvider initialSession={session}>
          <TabProvider>
            <DevLayout>
              {children}
            </DevLayout>
            <Analytics />
          </TabProvider>
        </AuthProvider>
      </body>
    </html>
  )
}
