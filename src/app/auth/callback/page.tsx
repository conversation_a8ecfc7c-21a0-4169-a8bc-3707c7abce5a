'use client'

import { useEffect } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { supabaseAuthClient } from '@/lib/auth/authService'

export default function AuthCallbackPage() {
  const router = useRouter()
  const searchParams = useSearchParams()

  useEffect(() => {
    const code = searchParams.get('code')
    const next = async () => {
      try {
        if (code) {
          // Exchange code; supabase-js handles cookie storage
          await supabaseAuthClient.auth.exchangeCodeForSession(code)
        }
      } catch (err) {
        console.error('Auth callback error:', err)
      } finally {
        // Always redirect to home or record page
        router.replace('/')
      }
    }
    next()
  }, [router, searchParams])

  return (
    <div className="flex justify-center items-center h-screen text-gray-500">
      Completing sign-in…
    </div>
  )
} 