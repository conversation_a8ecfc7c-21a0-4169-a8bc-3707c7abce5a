'use client'

import { Line } from 'react-chartjs-2'
import { TrendPoint } from '@/hooks/useEmotionTrend'
import { Chart as ChartJS, CategoryScale, LinearScale, PointElement, LineElement, Tooltip, Filler } from 'chart.js'

ChartJS.register(CategoryScale, LinearScale, PointElement, LineElement, Tooltip, Filler)

interface Props {
  points: TrendPoint[]
}

export default function EmotionTrendChart({ points }: Props) {
  // Build labels and data arrays from points
  const labels = points.map(p => {
    const d = new Date(p.timestamp)
    return d // placeholder, will format later
  })

  const dataArr = points.map(p => p.score)

  // Determine span and interval
  const spanMs = points.length > 1
    ? new Date(points[points.length - 1].timestamp).getTime() - new Date(points[0].timestamp).getTime()
    : 0

  const ONE_HOUR = 60 * 60 * 1000
  const ONE_DAY = 24 * ONE_HOUR

  let intervalMs: number
  let format: 'time' | 'datetime' | 'date'

  if (spanMs <= ONE_DAY) {
    // up to 1 day
    if (spanMs <= 6 * ONE_HOUR) {
      intervalMs = 30 * 60 * 1000 // 30 min
    } else {
      intervalMs = ONE_HOUR // 1 h
    }
    format = 'time'
  } else if (spanMs <= 3 * ONE_DAY) {
    intervalMs = 4 * ONE_HOUR // 4 h
    format = 'datetime'
  } else {
    intervalMs = ONE_DAY
    format = 'date'
  }

  // Build display labels ensuring first & last always labeled
  let lastLabelTs = 0
  const displayLabels = labels.map((dateObj, idx) => {
    const ts = (dateObj as unknown as Date).getTime()
    const isFirstOrLast = idx === 0 || idx === labels.length - 1
    if (isFirstOrLast || ts - lastLabelTs >= intervalMs) {
      lastLabelTs = ts
      const d = dateObj as unknown as Date
      switch (format) {
        case 'time':
          return d.toLocaleTimeString(undefined, { hour: '2-digit', minute: '2-digit' })
        case 'datetime':
          return d.toLocaleString(undefined, { month: '2-digit', day: '2-digit', hour: '2-digit' })
        case 'date':
        default:
          return d.toLocaleDateString(undefined, { month: '2-digit', day: '2-digit' })
      }
    }
    return ''
  })

  const data = {
    labels: displayLabels,
    datasets:[{
      label:'Emotion Score',
      data: dataArr,
      borderColor:'#52b6de',
      backgroundColor:'#52b6de',
      spanGaps:true,
      tension:0.3,
      pointRadius:4,
      pointHoverRadius:5,
      pointBackgroundColor:'#52b6de',
      pointBorderWidth:0,
      pointBorderColor:'#52b6de'
    }]
  }

  const options={
    responsive:true,
    plugins:{ tooltip:{ callbacks:{ label:(ctx:any)=> `Score: ${ctx.raw}` } } },
    scales:{
      y:{ display:false, grid:{display:false} },
      x:{ grid:{display:false}, ticks:{ color:'#6b7280', font:{ size:12 }, maxRotation:60, minRotation:30 } }
    }
  }

  return <Line data={data} options={{...options, maintainAspectRatio:false}} />
} 