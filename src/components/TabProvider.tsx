'use client'

import { createContext, useContext, useEffect, useState } from 'react'

type TabType = 'record' | 'history' | 'insight'

interface TabContextType {
  activeTab: TabType
  setActiveTab: (tab: TabType) => void
}

const TabContext = createContext<TabContextType | undefined>(undefined)

export function TabProvider({ children }: { children: React.ReactNode }) {
  const [activeTab, setActiveTab] = useState<TabType | null>(null)

  useEffect(() => {
    try {
      const stored = localStorage.getItem('sentari-active-tab') as TabType | null
      if (stored && (stored === 'record' || stored === 'history' || stored === 'insight')) {
        setActiveTab(stored)
      } else {
        setActiveTab('record')
      }
    } catch (_) {
      setActiveTab('record')
    }
  }, [])

  // Persist whenever it changes
  useEffect(() => {
    if (!activeTab) return
    try {
      localStorage.setItem('sentari-active-tab', activeTab)
    } catch (_) {}
  }, [activeTab])

  // Render nothing until initial tab determined to avoid flash
  if (!activeTab) return null;

  return (
    <TabContext.Provider value={{ activeTab, setActiveTab: setActiveTab as (tab: TabType) => void }}>
      {children}
    </TabContext.Provider>
  )
}

export function useTab() {
  const context = useContext(TabContext)
  if (context === undefined) {
    throw new Error('useTab must be used within a TabProvider')
  }
  return context
} 