'use client'

import { createContext, useContext } from 'react'
import { Session } from '@supabase/supabase-js'
import { useAuth } from '@/hooks/useAuth'
import { supabaseAuthClient } from '@/lib/auth/authService'

// ✅ 推荐标准用法：在客户端组件用 useSupabaseAuth()
const AuthContext = createContext<ReturnType<typeof useAuth>>({
  session: null,
  loading: true,
  signInWithGoogle: () => {},
  signOut: () => {},
  supabase: supabaseAuthClient,
})

export default function AuthProvider({ children, initialSession }: { children: React.ReactNode; initialSession?: Session | null }) {
  const auth = useAuth()
  return <AuthContext.Provider value={auth}>{children}</AuthContext.Provider>
}

// Custom hook to use auth context
export function useSupabaseAuth() {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error('useSupabaseAuth must be used within AuthProvider')
  }
  return context
} 