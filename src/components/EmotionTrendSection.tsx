'use client'

import { useState } from 'react'
import { useEmotionTrend } from '@/hooks/useEmotionTrend'
import EmotionTrend<PERSON>hart from './EmotionTrendChart'

export default function EmotionTrendSection() {
  const { data, loading, error, fetchTrend } = useEmotionTrend()
  const [flipped, setFlipped] = useState(false)

  const handleClick = async () => {
    if(!flipped && data.length===0){
      await fetchTrend()
    }
    setFlipped(!flipped)
  }

  return (
    <div className="flex flex-col items-center">
      <div
        onClick={handleClick}
        className={`relative w-full h-64 [transform-style:preserve-3d] transition-transform duration-700 ease-in-out cursor-pointer ${flipped ? 'rotate-y-180' : ''}`}
      >
        {/* Front Face */}
        <div className="absolute inset-0 flex items-center justify-center bg-[#fdf5eb] rounded-xl shadow-md shadow-orange-100 border border-[#fbead1] backface-hidden text-center p-4">
          <span className="text-xl font-semibold tracking-widest text-gray-600 select-none">
            {loading ? 'generating…' : 'reveal emotional trend'}
          </span>
        </div>

        {/* Back Face */}
        <div className="absolute inset-0 bg-white rounded-xl shadow-lg rotate-y-180 backface-hidden p-4 flex items-center justify-center overflow-hidden">
          {error && <p className="text-xs text-red-600 text-center">{error}</p>}
          {data.length > 0 ? (
            <div className="w-full h-full"><EmotionTrendChart points={data} /></div>
          ) : (
            <p className="text-center text-xl font-semibold tracking-widest text-gray-400">reveal emotional trend</p>
          )}
        </div>
      </div>
    </div>
  )
} 