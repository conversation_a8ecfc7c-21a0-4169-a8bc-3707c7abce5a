'use client'

import React from 'react'

interface RecordButtonProps {
  isRecording: boolean
  onClick: () => void
  /** Optional: current duration in seconds for progress ring */
  recordingDuration?: number
  /** Maximum duration to reach 100 % (defaults 180 s = 3 min) */
  maxDuration?: number
  /**
   * Optional size – numeric (pixels) or CSS string (vw, rem, etc.)
   * Defaults to `18vw` (consistent with existing design)
   */
  size?: number | string
}

// Pure-UI component – no business logic inside
export function RecordButton({
  isRecording,
  onClick,
  recordingDuration = 0,
  maxDuration = 180,
  size = '18vw',
}: RecordButtonProps) {
  // Normalize into style object so parent flex layout stays intact
  const dimension = typeof size === 'number' ? `${size}px` : size
  const baseStyle: React.CSSProperties = {
    width: dimension,
    height: dimension,
    minWidth: '120px',
    minHeight: '120px',
    maxWidth: '160px',
    maxHeight: '160px',
  }

  // ------- progress ring math -------
  const radius = 54 // viewBox radius (px) after padding
  const circumference = 2 * Math.PI * radius
  const progressRatio = Math.min(Math.max(recordingDuration / maxDuration, 0), 1)
  const dashOffset = circumference * (1 - progressRatio)

  // Color between soft orange and coral red
  const progressColor = progressRatio <= 0.5 ? '#FFCD94' : '#FF7070'

  return (
    <div className="relative" style={baseStyle}>
      {/* Progress Ring */}
      {isRecording && (
        <div className="absolute inset-0 flex items-center justify-center pointer-events-none z-20">
          <svg width="100%" height="100%" viewBox="0 0 120 120" fill="none">
            {/* background track */}
            <circle
              cx="60"
              cy="60"
              r={radius}
              stroke="#FFFFFF10" // ~6% opacity white
              strokeWidth="6"
            />
            {/* progress arc */}
            <circle
              cx="60"
              cy="60"
              r={radius}
              stroke={progressColor}
              strokeWidth="6"
              strokeLinecap="round"
              transform="rotate(-90 60 60)" // start at top
              style={{
                strokeDasharray: `${circumference}`,
                strokeDashoffset: dashOffset,
                transition: 'stroke-dashoffset 0.35s linear, stroke 0.35s linear',
              }}
            />
          </svg>
        </div>
      )}

      {/* Ripple effect layers */}
      {isRecording && (
        <>
          <span className="absolute inset-0 rounded-full bg-red-400 opacity-40 animate-ripple pointer-events-none" />
          {/* second delayed ring to create multi-ring illusion */}
          <span className="absolute inset-0 rounded-full bg-red-400 opacity-20 animate-ripple pointer-events-none [animation-delay:0.6s]" />
        </>
      )}

      {/* Soft Pulsing Glow */}
      <span
        className={`absolute inset-0 rounded-full blur-2xl pointer-events-none animate-glow ${
          isRecording ? 'bg-red-400 opacity-30' : 'bg-orange-300 opacity-25'
        }`}
        style={{ zIndex: 1 }}
      />

      {/* Core button */}
      <button
        type="button"
        aria-label={isRecording ? 'Stop recording' : 'Start recording'}
        onClick={onClick}
        className={`relative z-10 w-full h-full rounded-full flex items-center justify-center text-white text-3xl transition-all duration-300 ease-in-out transform hover:scale-105 focus:outline-none focus:ring-4 focus:ring-red-200 focus:ring-offset-2
          ${isRecording
            ? 'bg-gradient-to-br from-red-400 to-red-500 shadow-lg shadow-red-500/30'
            : 'bg-gradient-to-br from-orange-400 to-orange-500 shadow-lg shadow-orange-500/30'
          }`}
      >
        {isRecording ? '⏹️' : '🎙️'}
      </button>

      {/* Screen-reader live region */}
      <span aria-live="polite" className="sr-only">
        {isRecording ? 'Recording in progress' : 'Recording stopped'}
      </span>
    </div>
  )
} 