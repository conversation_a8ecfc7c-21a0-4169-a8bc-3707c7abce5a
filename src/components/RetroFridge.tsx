'use client'

import { useState, useMemo } from 'react'
import type { EmojiEntry } from '@/hooks/useRecentEmojiEntries'
import { format } from 'date-fns'
import { useDeviceType } from '@/hooks/useDeviceType'

interface Props {
  entries: EmojiEntry[]
  showNotes: boolean
  onHandleClick?: () => void
  processing?: boolean
  seed: number
}

export default function RetroFridge({ entries, showNotes, onHandleClick, processing = false, seed }: Props) {
  const [selectedId, setSelectedId] = useState<string | null>(null)
  const selected = useMemo(() => entries.find(e => e.id === selectedId) || null, [entries, selectedId])

  // determine device type (mobile / tablet / desktop) so we can tweak sizing
  const deviceType = useDeviceType()

  // deterministic random positions per id
  const positioned = useMemo(() => {
    return entries.map((e) => {
      const hash = Array.from(e.id).reduce((acc, ch) => acc + ch.charCodeAt(0), 0)
      const rand = Math.abs(Math.sin(seed + hash))
      const rand2 = Math.abs(Math.cos(seed + hash))
      const top = 14 + Math.floor(rand * 66) // 14–80 (avoid logo area)
      const left = 15 + Math.floor(rand2 * 60) // 15–75
      const rotate = (Math.floor(rand * 40) - 20) // -20 to +20

      const noteColors = ['bg-yellow-100','bg-red-100','bg-green-100','bg-blue-100','bg-pink-100','bg-purple-100','bg-orange-100']
      const pinColors = ['bg-red-500','bg-blue-500','bg-green-500','bg-purple-500','bg-rose-500','bg-orange-500']
      const noteColor = noteColors[Math.floor(rand * noteColors.length)]
      const pinColor = pinColors[Math.floor(rand2 * pinColors.length)]
      return { ...e, top, left, rotate, noteColor, pinColor }
    })
  }, [entries, seed])

  // determine position for the dummy "to-do list" sticky note so it moves each seed
  const todoPos = useMemo(() => {
    // create some randomness derived from the same seed but offset so it is unrelated to entry ids
    const rand = Math.abs(Math.sin(seed + 12345))
    const rand2 = Math.abs(Math.cos(seed + 67890))
    // Position above emoji notes but below the logo; adjust if near center
    let top = 9 + Math.floor(rand * 5) // 9–14 %
    const left = 10 + Math.floor(rand2 * 60) // 10–70 %
    // if horizontally near center (might cover logo), nudge further down
    if (left > 40 && left < 60) top = 14
    const rotate = -3 + (Math.floor(rand * 10) - 5) // slight variation around -3 deg
    return { top, left, rotate }
  }, [seed])

  return (
    <div className="relative w-full h-[420px] sm:h-[520px] mx-auto rounded-[40px] bg-gradient-to-b from-[#fff8eb] to-[#ffe9c0] border border-white ring-2 ring-white/70 shadow-[0_8px_16px_rgba(0,0,0,0.05)] transition-all duration-300 ease-in-out hover:scale-[1.01] overflow-hidden">
      {/* outer glow frame */}
      <div className="absolute inset-0 rounded-[40px] ring-1 ring-white/40 pointer-events-none" />

      {/* freezer dividing line */}
      <div className="absolute top-[40%] left-0 w-full h-[2px] bg-white/50" />

      {/* fridge logo (ensure highest z-index so notes never cover) */}
      <div className="absolute z-50 top-6 left-1/2 -translate-x-1/2 text-xl font-semibold tracking-widest text-gray-600 select-none pointer-events-none">
        the wall
      </div>

      {/* sticky notes with pin */}
      {showNotes && positioned.map(entry => {
        const isSelected = selectedId === entry.id
        const showAbove = entry.top > 50
        // note size and font scale depend on device to better fit small screens
        const noteSizeClass = deviceType === 'mobile' ? 'w-10 h-10 text-lg' : 'w-12 h-12 text-xl'
        return (
          <div
            key={entry.id}
            onClick={() => setSelectedId(isSelected ? null : entry.id)}
            className={`absolute flex items-center justify-center ${noteSizeClass} ${entry.noteColor} rounded-md shadow-md cursor-pointer transition-transform duration-200 ${isSelected ? 'z-30' : ''}`}
            style={{
              top: `${entry.top}%`,
              left: `${entry.left}%`,
              transform: `rotate(${entry.rotate}deg)`
            }}
          >
            {/* pin */}
            <div className={`absolute -top-1 left-1/2 -translate-x-1/2 w-2 h-2 ${entry.pinColor} rounded-full shadow-md`} />
            <div className="leading-none select-none">{entry.entry_emoji}</div>

            {/* popover relative to note */}
            {isSelected && (
              <div
                className="absolute bg-white p-3 rounded-md shadow-lg w-64 max-w-[250px] text-xs text-gray-800 whitespace-pre-wrap break-words"
                style={showAbove ? { bottom: '110%', left: '50%', transform: `translateX(-50%) rotate(${-entry.rotate}deg)` } : { top: '110%', left: '50%', transform: `translateX(-50%) rotate(${-entry.rotate}deg)` }}
              >
                <div className="mb-1 text-gray-500 text-[10px]">{format(new Date(entry.created_at), 'PPpp')}</div>
                {entry.transcript_user}
              </div>
            )}
          </div>
        )
      })}

      {/* handle button */}
      <button
        type="button"
        onClick={onHandleClick}
        disabled={processing}
        className="absolute left-4 bottom-24 w-20 h-5 flex items-center justify-center text-[10px] uppercase tracking-wider text-gray-600 bg-gradient-to-r from-gray-200 to-white rounded-full shadow-inner border border-gray-300 rotate-[-2deg] before:content-[''] before:absolute before:inset-0 before:bg-white/40 before:rounded-full disabled:opacity-50"
      >
        {processing ? '...' : 'Reveal'}
      </button>

      {/* Dummy larger sticky note (position varies with seed) */}
      <div
        className="absolute bg-yellow-200 rounded-md shadow-lg shadow-yellow-100 p-2 select-none relative"
        style={{
          top: `${todoPos.top}%`,
          left: `${todoPos.left}%`,
          transform: `rotate(${todoPos.rotate}deg)`,
          width: deviceType === 'mobile' ? '120px' : '140px',
          height: deviceType === 'mobile' ? '120px' : '140px'
        }}
      >
        {/* decorative pin emoji */}
        <div
          className="absolute top-[2%] left-1/2 text-3xl leading-none select-none"
          style={{ transform: 'translateX(-50%) scale(0.7)' }}
        >
          📌
        </div>
        <p className="text-xs font-semibold text-black mt-6">to do list</p>
      </div>
    </div>
  )
} 