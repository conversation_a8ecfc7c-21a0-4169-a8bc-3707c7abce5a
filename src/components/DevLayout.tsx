'use client'

import { useTab } from '@/components/TabProvider'
import { useSupabaseAuth } from '@/components/AuthProvider'
import HeaderNav from './HeaderNav'

export default function DevLayout({ children }: { children: React.ReactNode }) {
  const { activeTab, setActiveTab } = useTab()
  const { session, signInWithGoogle, signOut } = useSupabaseAuth()

  return (
    <div className="min-h-screen bg-gray-50">
      <HeaderNav />
      <main className="container mx-auto px-4 py-8">
        {children}
      </main>
    </div>
  )
} 