'use client'

import React, { useState, useCallback, useEffect } from 'react'
import { useSupabaseAuth } from '@/components/AuthProvider'
import { useHistory, EditState, EditStateItem, TAG_CATEGORIES } from '@/hooks/useHistory'
import { Edit2, Trash2, X, Check, Search } from 'lucide-react'
import debounce from 'lodash/debounce'
import { motion, AnimatePresence } from 'framer-motion'

// Inner voice-like search placeholders
const SEARCH_PLACEHOLDERS = [
  "What did you once wonder?",
  "Start with a word you remember...",
  "Search your thoughts, stories, or even jokes",
  "What mattered to you last week?",
  "Something about... a name? a dream?",
  "Search #tags or phrases you once said",
  "Remember that feeling when...",
  "That conversation about...",
  "Was it yesterday when you thought...",
  "Looking for a specific moment?",
]

// Placeholder transition timing (in milliseconds)
const PLACEHOLDER_TIMING = {
  FADE_DURATION: 600,
  DISPLAY_DURATION: 4000,
}

// Tag type mapping for styling
const TAG_STYLES: Record<string, { bg: string, text: string }> = {
  emotion: {
    bg: 'bg-tag-emotion-bg',
    text: 'text-tag-emotion-text'
  },
  reflection: {
    bg: 'bg-tag-reflection-bg',
    text: 'text-tag-reflection-text'
  },
  idea: {
    bg: 'bg-tag-idea-bg',
    text: 'text-tag-idea-text'
  }
}

const getTagStyle = (tag: string) => {
  // Remove # prefix if exists
  const cleanTag = tag.toLowerCase().replace('#', '')
  
  // Find which category the tag belongs to
  const category = Object.entries(TAG_CATEGORIES).find(([_, tags]) => 
    tags.includes(cleanTag)
  )?.[0]

  return TAG_STYLES[category || 'default'] || { bg: 'bg-gray-100', text: 'text-gray-600' }
}

export default function HistoryTab() {
  const { session, signInWithGoogle } = useSupabaseAuth()
  const {
    entries,
    loading,
    error,
    editState,
    saveEditedTranscript,
    deleteEntry,
    setEditState,
    updateEditState,
    refresh,
    searchQuery,
    handleSearch,
    isSearching,
  } = useHistory()

  // Rotating placeholder text with smooth scroll effect
  const [placeholderIndex, setPlaceholderIndex] = useState(0)
  const [isPlaceholderChanging, setIsPlaceholderChanging] = useState(false)
  
  useEffect(() => {
    const interval = setInterval(() => {
      setIsPlaceholderChanging(true)
      
      // After animation completes, change text
      setTimeout(() => {
        setPlaceholderIndex((prev) => (prev + 1) % SEARCH_PLACEHOLDERS.length)
        setIsPlaceholderChanging(false)
      }, PLACEHOLDER_TIMING.FADE_DURATION)
      
    }, PLACEHOLDER_TIMING.DISPLAY_DURATION)
    
    return () => clearInterval(interval)
  }, [])

  // Debounced search handler
  const debouncedSearch = useCallback(
    debounce((query: string) => {
      handleSearch(query)
    }, 300),
    [handleSearch]
  )

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1)
  const entriesPerPage = 10
  const totalPages = Math.ceil(entries.length / entriesPerPage)
  const currentEntries = entries.slice(
    (currentPage - 1) * entriesPerPage,
    currentPage * entriesPerPage
  )

  // Delete confirmation state
  const [deleteConfirmation, setDeleteConfirmation] = useState<{
    isOpen: boolean;
    entryId: string | null;
    error: string | null;
  }>({
    isOpen: false,
    entryId: null,
    error: null,
  })

  // Handle delete confirmation
  const handleDeleteClick = (entryId: string) => {
    setDeleteConfirmation({
      isOpen: true,
      entryId,
      error: null,
    })
  }

  const handleConfirmDelete = async () => {
    if (!deleteConfirmation.entryId) return

    const result = await deleteEntry(deleteConfirmation.entryId)
    
    if (result.success) {
      setDeleteConfirmation({
        isOpen: false,
        entryId: null,
        error: null,
      })
    } else {
      setDeleteConfirmation(prev => ({
        ...prev,
        error: result.error,
      }))
    }
  }

  const handleCancelDelete = () => {
    setDeleteConfirmation({
      isOpen: false,
      entryId: null,
      error: null,
    })
  }

  // If not authenticated, show login message
  if (!session) {
    return (
      <div className="text-center py-8 text-gray-500 font-sans space-y-4">
        <p>Please login to view history</p>
        <button
          onClick={() => signInWithGoogle()}
          className="px-4 py-2 bg-orange-500 text-white rounded-full shadow-md hover:bg-orange-600 transition-colors duration-200"
        >
          Login
        </button>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-[#FFF9F2] px-4 py-6 font-sans">
      <div className="max-w-2xl mx-auto space-y-6">
        {/* Search Bar */}
        <div className="relative mb-6">
          <div className="relative flex items-center">
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => handleSearch(e.target.value)}
              className="w-full h-12 pl-12 pr-4 bg-white rounded-2xl shadow-sm focus:outline-none focus:ring-2 focus:ring-orange-200 text-gray-700 placeholder-transparent peer"
              placeholder={SEARCH_PLACEHOLDERS[placeholderIndex]}
            />
            <label
              className="absolute left-12 transition-all duration-200 pointer-events-none
                peer-placeholder-shown:text-gray-400 peer-placeholder-shown:top-1/2 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:text-base
                peer-focus:text-xs peer-focus:text-gray-500 peer-focus:top-1 peer-focus:translate-y-0
                text-xs text-gray-500 top-1 translate-y-0"
            >
              {SEARCH_PLACEHOLDERS[placeholderIndex]}
            </label>
            <div className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-400">
              <Search size={20} />
            </div>
          </div>
        </div>

        {/* Delete Confirmation Modal */}
        {deleteConfirmation.isOpen && (
          <div 
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
            role="dialog"
            aria-modal="true"
            onClick={handleCancelDelete}
          >
            <div 
              className="bg-white rounded-2xl p-6 max-w-sm w-full mx-4 shadow-lg"
              onClick={(e) => e.stopPropagation()}
            >
              <h3 className="text-lg font-medium mb-3">Delete Entry</h3>
              <p className="text-gray-600 mb-6">Are you sure you want to delete this entry? This action cannot be undone.</p>
              {deleteConfirmation.error && (
                <div className="mb-4 p-3 bg-red-50 text-red-600 rounded-lg text-sm">
                  {deleteConfirmation.error}
                </div>
              )}
              <div className="flex justify-end gap-3">
                <button
                  onClick={handleCancelDelete}
                  className="p-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-gray-300 focus:ring-offset-2"
                >
                  <X size={20} />
                </button>
                <button
                  onClick={handleConfirmDelete}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' || e.key === ' ') {
                      e.preventDefault();
                      handleConfirmDelete();
                    }
                  }}
                  className="p-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
                  autoFocus
                >
                  <Check size={20} />
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Loading and Error States */}
        {error ? (
          <div className="text-red-600 text-sm bg-red-50 p-3 rounded-lg">
            {error}
          </div>
        ) : loading ? (
          <div className="text-orange-600 text-sm flex items-center justify-center gap-2 p-8">
            <div className="animate-spin rounded-full h-4 w-4 border-2 border-orange-600"></div>
            Loading entries...
          </div>
        ) : entries.length === 0 ? (
          <div className="text-gray-500 text-center py-8">
            {searchQuery ? 'No matching entries found' : 'No entries found. Try recording something first!'}
          </div>
        ) : (
          <>
            {/* Entries List */}
            <div className="space-y-4">
              {currentEntries.map((entry) => {
                const state = editState[entry.id || entry.created_at]
                if (!state) return null

                return (
                  <div key={entry.id || entry.created_at} className="bg-white rounded-2xl shadow-sm px-6 py-4">
                    {/* Header with Date and Actions */}
                    <div className="flex justify-between items-center mb-4">
                      <div className="text-xs text-gray-400">
                        {new Date(entry.created_at).toLocaleString()}
                      </div>
                      <div className="flex space-x-3">
                        {state.isEditingTranscript && (
                          <>
                            <button
                              onClick={() => updateEditState(entry.id || entry.created_at, { isEditingTranscript: false })}
                              className="text-gray-400 hover:text-gray-600 hover:scale-110 transition"
                              title="Cancel edit"
                            >
                              <X size={18} />
                            </button>
                            <button
                              onClick={async () => {
                                await saveEditedTranscript(entry.id || entry.created_at)
                              }}
                              className="text-green-600 hover:text-green-700 hover:scale-110 transition"
                              title="Save changes"
                              disabled={state.transcriptSaveStatus === 'saving'}
                            >
                              <Check size={18} />
                            </button>
                          </>
                        )}
                        <button
                          onClick={() => {
                            updateEditState(entry.id || entry.created_at, {
                              isEditingTranscript: true,
                              editableTranscript: entry.transcript_user
                            })
                          }}
                          className="text-gray-400 hover:text-gray-600 hover:scale-110 transition"
                          title="Edit entry"
                        >
                          <Edit2 size={18} />
                        </button>
                        <button
                          onClick={() => handleDeleteClick(entry.id || entry.created_at)}
                          className="text-gray-400 hover:text-red-500 hover:scale-110 transition"
                          title="Delete entry"
                        >
                          <Trash2 size={18} />
                        </button>
                      </div>
                    </div>

                    {/* Content */}
                    {!state.isEditingTranscript ? (
                      <div className="space-y-2">
                        <div className="text-[17px] text-gray-600 font-normal leading-relaxed whitespace-pre-wrap">
                          {entry.transcript_user}
                        </div>
                        {/* Tags */}
                        {entry.tags_user && entry.tags_user.length > 0 && (
                          <div className="flex flex-wrap gap-2 mt-3">
                            {entry.tags_user.map((tag, index) => {
                              // Skip null or undefined tags
                              if (!tag) return null
                              
                              const { bg, text } = getTagStyle(tag)
                              return (
                                <span
                                  key={`tag-${tag}-${index}`}
                                  className={`${bg} ${text} text-xs px-3 py-1 rounded-full`}
                                >
                                  #{tag}
                                </span>
                              )
                            })}
                          </div>
                        )}
                      </div>
                    ) : (
                      <div className="space-y-3">
                        <textarea
                          value={state.editableTranscript}
                          onChange={(e) => updateEditState(entry.id || entry.created_at, { editableTranscript: e.target.value })}
                          className="w-full p-3 text-[17px] text-gray-600 font-normal border border-gray-200 rounded-xl min-h-[120px] resize-none focus:outline-none focus:ring-1 focus:ring-gray-300"
                          autoFocus
                        />
                      </div>
                    )}
                  </div>
                )
              })}
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="flex justify-center gap-2 pt-6">
                {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                  <button
                    key={page}
                    onClick={() => setCurrentPage(page)}
                    className={`w-8 h-8 rounded-full text-sm transition-colors ${
                      currentPage === page
                        ? 'bg-gray-800 text-white'
                        : 'bg-white text-gray-600 hover:bg-gray-100'
                    }`}
                  >
                    {page}
                  </button>
                ))}
              </div>
            )}
          </>
        )}
      </div>
    </div>
  )
} 