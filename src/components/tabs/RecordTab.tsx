'use client'

import React, { useState, useEffect } from 'react'
import { useRecordingFlow } from '@/hooks/useRecordingFlow'
import { useSupabaseAuth } from '@/components/AuthProvider'
import { TAG_CATEGORIES } from '@/hooks/useHistory'
import { RecordButton } from '@/components/RecordButton'
import { processEmpathy, ApiResponse } from '@/lib/api/apiService'

// Tag type mapping for styling
const TAG_STYLES: Record<string, { bg: string, text: string }> = {
  emotion: {
    bg: 'bg-tag-emotion-bg',
    text: 'text-tag-emotion-text'
  },
  reflection: {
    bg: 'bg-tag-reflection-bg',
    text: 'text-tag-reflection-text'
  },
  idea: {
    bg: 'bg-tag-idea-bg',
    text: 'text-tag-idea-text'
  },
  default: {
    bg: 'bg-orange-50',
    text: 'text-orange-500'
  }
}

const getTagStyle = (tag: string) => {
  // Remove # prefix if exists
  const cleanTag = tag.toLowerCase().replace('#', '')
  
  // Find which category the tag belongs to
  const category = Object.entries(TAG_CATEGORIES).find(([_, tags]) => 
    tags.includes(cleanTag)
  )?.[0]

  return TAG_STYLES[category || 'default'] || { bg: 'bg-gray-100', text: 'text-gray-600' }
}

export default function RecordTab() {
  const {
    // recording
    isRecording,
    recordingDuration,
    startRecording,
    stopRecording,
    
    // transcript
    transcript,
    editableTranscript,
    setEditableTranscript,
    isEditingTranscript,
    setIsEditingTranscript,
    transcriptSaveStatus,
    saveEditedTranscript,
    resetTranscriptStatus,

    // tags
    editableTags,
    saveResponse,
  } = useRecordingFlow()

  const { session, signInWithGoogle } = useSupabaseAuth()

  const [empathyReply, setEmpathyReply] = useState('')

  // Fetch empathy reply whenever transcript is saved (non-empty and not editing)
  useEffect(() => {
    const fetchReply = async () => {
      if (!transcript || isEditingTranscript) return
      try {
        const response: ApiResponse = await processEmpathy(transcript, session?.user?.id || 'anon')
        
        if (!response.success) {
          throw new Error(response.error || 'Failed to process empathy')
        }
        
        if (response.data?.response_text) {
          console.log('🟢 GPT Reply:', response.data.response_text)
          setEmpathyReply(response.data.response_text)
        }
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred'
        console.error('🟥 Empathy API error:', errorMessage)
      }
    }

    fetchReply()
  }, [transcript, isEditingTranscript, session?.user?.id])

  // Clear previous reply when a new recording starts
  useEffect(() => {
    if (isRecording) {
      setEmpathyReply('')
    }
  }, [isRecording])

  if (!session) {
    return (
      <div className="text-center py-8 text-gray-500 space-y-4">
        <p>Please login to record</p>
        <button
          onClick={() => signInWithGoogle()}
          className="px-4 py-2 bg-orange-500 text-white rounded-full shadow-md hover:bg-orange-600 transition-colors duration-200"
        >
          Login
        </button>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-[#FFF9F2] p-4 relative overflow-hidden">
      {/* Flowing sunset gradient overlay */}
      <div className="pointer-events-none fixed inset-0 -z-10 overflow-hidden">
        <div
          className="absolute inset-0 bg-gradient-to-r from-[#FFD8A3] via-[#FFC1B7] to-[#FFAFB0] opacity-15 animate-flow-x"
          style={{ backgroundSize: '300% 100%' }}
        />
      </div>
      <div className="max-w-2xl mx-auto flex flex-col items-center space-y-6 pt-12">
        {/* Breathing Text */}
        <div 
          className="h-[calc(18vw*0.2)] min-h-[24px] max-h-[32px] flex items-center justify-center relative z-30 pointer-events-none"
          style={{ 
            animation: 'breathing 6s ease-in-out infinite',
          }}
        >
          <div className="text-gray-400 text-sm md:text-base font-light tracking-wide">
            Just speak. One thought at a time.
          </div>
        </div>

        {/* Recording Button (UI-only) */}
        <RecordButton
          isRecording={isRecording}
          recordingDuration={recordingDuration}
          onClick={isRecording ? stopRecording : startRecording}
        />

        {/* Recording Duration */}
        {isRecording && (
          <div className="text-orange-600 font-medium animate-pulse">
            Recording... {recordingDuration}s
          </div>
        )}

        {/* Transcript Section */}
        {!isRecording && (
          <div className="w-full relative">
            {/* Main Card */}
            <div className="bg-white rounded-3xl shadow-lg p-6">
              <div className="flex justify-between items-center mb-4">
                {transcript && <h3 className="font-medium text-gray-700">Transcript</h3>}
                <div className="flex gap-2 ml-auto">
                  {!isEditingTranscript && transcript && (
                    <button
                      onClick={() => setIsEditingTranscript(true)}
                      className="px-4 py-1.5 text-sm text-orange-500 hover:text-orange-600 hover:bg-orange-50 rounded-full transition-all duration-300 animate-fade-in"
                      disabled={!transcript}
                    >
                      Edit
                    </button>
                  )}
                  {isEditingTranscript && (
                    <>
                      <button
                        onClick={saveEditedTranscript}
                        className="px-4 py-1.5 text-sm bg-orange-500 text-white rounded-full hover:bg-orange-600 transition-colors"
                        disabled={transcriptSaveStatus === 'saving'}
                      >
                        {transcriptSaveStatus === 'saving' ? 'Saving...' : 'Save'}
                      </button>
                      <button
                        onClick={() => {
                          setIsEditingTranscript(false)
                          setEditableTranscript(transcript)
                          resetTranscriptStatus()
                        }}
                        className="px-4 py-1.5 text-sm text-gray-500 hover:text-gray-600 hover:bg-gray-50 rounded-full transition-colors"
                      >
                        Cancel
                      </button>
                    </>
                  )}
                </div>
              </div>

              {/* Status Messages */}
              {transcriptSaveStatus === 'saved' && (
                <div className="mb-4 text-sm text-green-700 bg-green-50 px-4 py-2 rounded-full">
                  ✨ Saved successfully
                </div>
              )}
              {transcriptSaveStatus === 'error' && (
                <div className="mb-4 text-sm text-red-700 bg-red-50 px-4 py-2 rounded-full">
                  Something went wrong. Please try again.
                </div>
              )}

              {/* Transcript Content */}
              {!isEditingTranscript ? (
                <div className="space-y-4">
                  <div className="text-[17px] text-gray-600 font-normal leading-relaxed whitespace-pre-wrap">
                    {transcript || 'Your thoughts will appear here...'}
                  </div>
                  {/* Tags Display */}
                  <div className="flex flex-wrap gap-2 mt-3">
                    {(editableTags?.length > 0 ? editableTags : saveResponse?.analysis?.selectedTags || []).map((tag: string, index: number) => {
                      // Skip null or undefined tags
                      if (!tag) return null
                      
                      const { bg, text } = getTagStyle(tag)
                      return (
                        <span
                          key={`tag-${tag}-${index}`}
                          className={`${bg} ${text} text-xs px-3 py-1 rounded-full cursor-default animate-fade-in`}
                          title={`Tag: ${tag}`}
                        >
                          #{tag}
                        </span>
                      )
                    })}
                  </div>
                </div>
              ) : (
                <textarea
                  value={editableTranscript}
                  onChange={(e) => setEditableTranscript(e.target.value)}
                  className="w-full p-6 border border-orange-100 rounded-2xl min-h-[200px] resize-none focus:outline-none focus:ring-2 focus:ring-orange-200 text-gray-600"
                  placeholder="Edit your thoughts here..."
                  autoFocus
                />
              )}
            </div>
          </div>
        )}
      </div>

      {/* Empathy Reply Box */}
      {!isRecording && empathyReply && (
        <div className="max-w-2xl mx-auto mt-6 animate-fade-in">
          <div className="bg-white rounded-3xl shadow-md p-6 space-y-3">
            {/* Title */}
            <h3 className="font-medium text-[17px] text-orange-500">Inner Voice</h3>
            {/* Reply text */}
            <p className="text-[17px] leading-relaxed whitespace-pre-line break-words text-orange-500">
              {empathyReply}
            </p>
          </div>
        </div>
      )}

      {/* Debug log for mobile error display */}
      <p id="debug-log" className="text-red-600 font-bold text-center mt-4"></p>

      {/* Add breathing animation keyframes */}
      <style jsx global>{`
        @keyframes breathing {
          0%, 100% {
            opacity: 0.6;
            transform: translateY(0);
          }
          50% {
            opacity: 1;
            transform: translateY(-2px);
          }
        }
      `}</style>
    </div>
  )
} 