'use client'

import { useSupabaseAuth } from '@/components/AuthProvider'
import RetroFridgeSection from '@/components/RetroFridgeSection'
import EmotionTrendSection from '@/components/EmotionTrendSection'

export default function InsightTab() {
  const { session, signInWithGoogle } = useSupabaseAuth()

  if (!session) {
    return (
      <div className="text-center py-8 text-gray-500 space-y-4">
        <p>Please login to view insights</p>
        <button
          onClick={() => signInWithGoogle()}
          className="px-4 py-2 bg-orange-500 text-white rounded-full shadow-md hover:bg-orange-600 transition-colors duration-200"
        >
          Login
        </button>
      </div>
    )
  }

  const upcomingFeatures = [
    {
      title: "Emoji Mood Timeline",
      description: "Visualize your daily emotions with expressive emojis, making your emotional journey instantly understandable.",
      icon: "😊",
      appeal: "Visual, engaging, and shareable - see your mood patterns at a glance!"
    },
    {
      title: "Aspiration & Intent Recognition",
      description: "Identifies expressions like 'I want...' to build your wishlist and track progress towards your goals.",
      icon: "🎯",
      appeal: "Personalized goal tracking that strengthens motivation and future orientation"
    },
    {
      title: "Unsent Letters Generator",
      description: "Express unspoken thoughts and release suppressed emotions in a private, therapeutic way.",
      icon: "✉️",
      appeal: "Deep emotional resonance with complete privacy - perfect for processing complex feelings"
    },
    {
      title: "Impulse Reflection Tool",
      description: "Document impulsive behaviors and understand your emotional triggers for better self-control.",
      icon: "🤔",
      appeal: "Practical tool for real emotional challenges - improve behavior through understanding"
    },
    {
      title: "Subconscious Idea Capture",
      description: "Never lose those 'just had an idea' moments - capture fleeting inspiration instantly.",
      icon: "💡",
      appeal: "Perfect for creators and dreamers - turn sudden insights into lasting ideas"
    }
  ]

  return (
    <div className="space-y-8">

      <RetroFridgeSection />

      <EmotionTrendSection />

      <div className="bg-white rounded-lg shadow-lg p-6">
        <h2 className="text-2xl font-semibold mb-6">✨ Coming Soon</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {upcomingFeatures.map((feature, index) => (
            <div key={index} className="bg-gray-50 rounded-lg p-6 hover:shadow-md transition-shadow">
              <div className="flex items-center gap-3 mb-3">
                <span className="text-3xl">{feature.icon}</span>
                <h3 className="text-xl font-semibold">{feature.title}</h3>
              </div>
              <p className="text-gray-600 mb-4">{feature.description}</p>
              <p className="text-sm text-indigo-600 italic">{feature.appeal}</p>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
} 