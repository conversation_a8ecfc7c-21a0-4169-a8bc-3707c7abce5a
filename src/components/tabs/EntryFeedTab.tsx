'use client'

import React from 'react'
import { useSupabaseAuth } from '@/components/AuthProvider'
import { useEntryFeed } from '@/hooks/useEntryFeed'
import { format } from 'date-fns'

export default function EntryFeedTab() {
  // ✅ 推荐标准用法：在客户端组件用
  const { session, signInWithGoogle } = useSupabaseAuth()
  const {
    entries,
    loading,
    error,
    hasMore,
    filters,
    availableTags,
    toggleTagFilter,
    setDateFilter,
    loadMore,
  } = useEntryFeed(10)
  
  // If not authenticated, show login message
  if (!session) {
    return (
      <div className="text-center py-8 text-gray-500 space-y-4">
        <p>请先登录查看历史记录</p>
        <button
          onClick={() => signInWithGoogle()}
          className="px-4 py-2 bg-orange-500 text-white rounded-full shadow-md hover:bg-orange-600 transition-colors duration-200"
        >
          登录
        </button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Filter Section */}
      <div className="bg-white p-4 rounded-lg shadow">
        <h2 className="text-lg font-semibold mb-4">Filters</h2>
        
        {/* Tag Filters */}
        <div className="mb-4">
          <h3 className="text-sm font-medium mb-2">Tags</h3>
          <div className="flex flex-wrap gap-2">
            {availableTags.map((tag, index) => {
              // Skip null or undefined tags
              if (!tag) return null
              
              return (
                <button
                  key={`filter-tag-${tag}-${index}`}
                  onClick={() => toggleTagFilter(tag)}
                  className={`px-3 py-1 rounded-full text-sm ${
                    filters.tags.includes(tag)
                      ? 'bg-blue-100 text-blue-800'
                      : 'bg-gray-100 text-gray-700'
                  }`}
                >
                  {tag}
                </button>
              )
            })}
          </div>
        </div>
        
        {/* Date Filters */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium mb-1">Start Date</label>
            <input
              type="date"
              onChange={(e) => setDateFilter('start', e.target.value)}
              className="w-full border rounded p-2"
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">End Date</label>
            <input
              type="date"
              onChange={(e) => setDateFilter('end', e.target.value)}
              className="w-full border rounded p-2"
            />
          </div>
        </div>
      </div>

      {/* Entries List */}
      <div className="space-y-4">
        {entries.map(entry => (
          <div key={entry.id} className="bg-white p-4 rounded-lg shadow">
            <div className="flex justify-between items-start mb-2">
              <div className="text-sm text-gray-500">
                {format(new Date(entry.created_at), 'PPP p')}
              </div>
            </div>
            <p className="text-gray-800 mb-3">{entry.transcript_user}</p>
            <div className="flex flex-wrap gap-2">
              {entry.tags_user?.map((tag, index) => {
                // Skip null or undefined tags
                if (!tag) return null
                
                return (
                  <span
                    key={`tag-${tag}-${index}`}
                    className="px-2 py-1 bg-gray-100 text-gray-700 rounded-full text-sm"
                  >
                    {tag}
                  </span>
                )
              })}
            </div>
          </div>
        ))}

        {/* Load More Button */}
        {hasMore && (
          <button
            onClick={loadMore}
            disabled={loading}
            className="w-full py-2 text-center text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
          >
            {loading ? 'Loading...' : 'Load More'}
          </button>
        )}

        {/* Error Message */}
        {error && (
          <div className="text-center text-red-600 py-4">
            {error}
          </div>
        )}
      </div>
    </div>
  )
} 