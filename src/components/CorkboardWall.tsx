'use client'

import { useState, useMemo } from 'react'
import type { EmojiEntry } from '@/hooks/useRecentEmojiEntries'
import { format } from 'date-fns'

interface Props {
  entries: EmojiEntry[]
}

export default function CorkboardWall({ entries }: Props) {
  const [selectedId, setSelectedId] = useState<string | null>(null)
  const selected = useMemo(() => entries.find(e => e.id === selectedId) || null, [entries, selectedId])

  const positioned = useMemo(() => {
    return entries.map(e => {
      const seed = Array.from(e.id).reduce((acc, ch) => acc + ch.charCodeAt(0), 0)
      const top = 10 + ((seed * 29) % 70) // 10-80
      const left = 10 + ((seed * 47) % 70) // 10-80
      const rotate = ((seed * 17) % 60) - 30 // -30 to +29
      const summary = e.transcript_user.length > 24 ? e.transcript_user.slice(0, 24) + '…' : e.transcript_user
      return { ...e, top, left, rotate, summary }
    })
  }, [entries])

  return (
    <div className="relative w-full h-[600px] bg-[#e0c8a6] bg-[radial-gradient(#d4b893_1px,transparent_1px)] bg-[12px_12px] rounded-xl shadow-inner overflow-hidden">
      {positioned.map(entry => (
        <div
          key={entry.id}
          onClick={() => setSelectedId(entry.id)}
          className="absolute w-28 h-28 p-2 bg-yellow-100 rounded-md shadow-md cursor-pointer hover:scale-105 transition-transform duration-200"
          style={{
            top: `${entry.top}%`,
            left: `${entry.left}%`,
            transform: `rotate(${entry.rotate}deg)`
          }}
        >
          {/* Pin */}
          <div className="absolute -top-1 left-1/2 -translate-x-1/2 w-2 h-2 bg-red-600 rounded-full shadow-md" />
          {/* Emoji */}
          <div className="text-xl mb-1">{entry.entry_emoji}</div>
          <div className="text-sm text-gray-800 line-clamp-3 break-words">{entry.summary}</div>
        </div>
      ))}

      {selected && (
        <div className="fixed top-20 left-1/2 -translate-x-1/2 z-50 bg-white rounded-lg shadow-xl p-4 w-72 max-w-full">
          <div className="text-3xl mb-2">{selected.entry_emoji}</div>
          <p className="text-sm text-gray-700 whitespace-pre-wrap break-words max-h-60 overflow-auto">
            {selected.transcript_user}
          </p>
          <p className="text-xs text-gray-400 mt-2">{format(new Date(selected.created_at), 'PPpp')}</p>
          <button
            onClick={() => setSelectedId(null)}
            className="mt-4 text-blue-500 text-sm underline"
          >
            关闭
          </button>
        </div>
      )}
    </div>
  )
} 