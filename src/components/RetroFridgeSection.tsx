'use client'

import RetroFridge from './RetroFridge'
import { useRecentEmojiEntries } from '@/hooks/useRecentEmojiEntries'
import { useState } from 'react'
import { pickEmojiBatch, ApiResponse } from '@/lib/api/apiService'

export default function RetroFridgeSection() {
  const { entries, loading, error, refresh } = useRecentEmojiEntries(15)
  const [generating, setGenerating] = useState(false)
  const [revealed, setRevealed] = useState(false)
  const [seed, setSeed] = useState(Date.now())

  const wallContent = (
    <RetroFridge
      entries={entries}
      showNotes={revealed}
      processing={generating}
      seed={seed}
      onHandleClick={async () => {
        try {
          setGenerating(true)
          
          const response: ApiResponse = await pickEmojiBatch(15)
          
          if (!response.success) {
            throw new Error(response.error || 'Failed to generate batch emojis')
          }
          
          console.log('✅ Batch emoji generation completed:', response.data)
          
          await refresh()
          setSeed(Date.now())
          setRevealed(true)
        } catch (err) {
          const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred'
          console.error('🟥 Batch emoji error:', errorMessage)
        } finally {
          setGenerating(false)
        }
      }}
    />
  )

  return (
    <div className="space-y-2 w-full flex justify-center">
      <div className="w-full max-w-xs sm:max-w-md md:max-w-lg lg:max-w-xl xl:max-w-2xl">
        {wallContent}

        {loading && <p className="text-center text-xs text-gray-400">Updating…</p>}
        {error && <p className="text-center text-red-600 text-xs">{error.message}</p>}
      </div>
    </div>
  )
} 