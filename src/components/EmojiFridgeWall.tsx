'use client'

import { useState, useMemo } from 'react'
import { useRecentEmojiEntries } from '@/hooks/useRecentEmojiEntries'
import { format } from 'date-fns'

export default function EmojiFridgeWall() {
  const { entries, loading, error, refresh } = useRecentEmojiEntries(50)
  const [selectedId, setSelectedId] = useState<string | null>(null)

  const selectedEntry = useMemo(() => entries.find(e => e.id === selectedId), [entries, selectedId])

  // Pre-compute random positions deterministically using id hash so they stay put across renders
  const positioned = useMemo(() => {
    return entries.map(e => {
      const seed = Array.from(e.id).reduce((acc, ch) => acc + ch.charCodeAt(0), 0)
      const top = 10 + ((seed * 37) % 80) // 10-90% range
      const left = 5 + ((seed * 53) % 90) // 5-95% range
      const rotate = (seed * 17) % 30 - 15
      return { ...e, top, left, rotate }
    })
  }, [entries])

  if (loading) return <p className="text-gray-500">Loading emojis…</p>
  if (error) return (
    <pre className="bg-red-50 text-red-600 p-3 text-sm whitespace-pre-wrap">
      {JSON.stringify({ message: error.message, stack: error.stack }, null, 2)}
    </pre>
  )

  return (
    <div className="relative w-full max-w-md h-[500px] mx-auto bg-gray-100 rounded-3xl border border-white shadow-inner overflow-hidden">
      {/* Fridge double-door divider */}
      <div className="absolute inset-y-0 left-1/2 w-[2px] bg-white/50 z-10" />

      {positioned.map(entry => (
        <button
          key={entry.id}
          style={{
            top: `${entry.top}%`,
            left: `${entry.left}%`,
            transform: `rotate(${entry.rotate}deg)`
          }}
          className="absolute text-3xl hover:scale-125 transition-transform focus:outline-none"
          onClick={() => setSelectedId(entry.id)}
        >
          {entry.entry_emoji}
        </button>
      ))}

      {/* Popover */}
      {selectedEntry && (
        <div className="fixed z-50 bg-white shadow-lg rounded-xl p-4 w-64 top-20 left-1/2 -translate-x-1/2">
          <p className="text-sm text-gray-600 mb-2">{format(new Date(selectedEntry.created_at), 'PPpp')}</p>
          <p className="text-base text-gray-800 whitespace-pre-wrap break-words max-h-48 overflow-auto">
            {selectedEntry.transcript_user}
          </p>
          <button
            className="mt-2 text-sm text-blue-500 hover:underline"
            onClick={() => setSelectedId(null)}
          >
            关闭
          </button>
        </div>
      )}

      {/* Refresh button */}
      <button
        onClick={refresh}
        className="absolute bottom-2 right-2 text-xs text-gray-500 bg-white/70 rounded px-2 py-1 hover:bg-white"
      >
        Refresh
      </button>
    </div>
  )
} 