'use client'

import CorkboardWall from './CorkboardWall'
import { useRecentEmojiEntries } from '@/hooks/useRecentEmojiEntries'
import { useState } from 'react'
import { pickEmojiBatch, ApiResponse } from '@/lib/api/apiService'

export default function CorkboardSection() {
  const { entries, loading, error, refresh } = useRecentEmojiEntries(50)
  const [generating, setGenerating] = useState(false)

  if (loading) return <p className="text-center text-gray-500">Loading board…</p>
  if (error) return <p className="text-center text-red-600">{error.message}</p>
  if (entries.length === 0) return <p className="text-center text-gray-500">No emoji entries yet.</p>

  return (
    <div className="space-y-2">
      <CorkboardWall entries={entries} />
      <button
        onClick={async () => {
          try {
            setGenerating(true)
            
            const response: ApiResponse = await pickEmojiBatch(15)
            
            if (!response.success) {
              throw new Error(response.error || 'Failed to generate batch emojis')
            }
            
            console.log('✅ Batch emoji generation completed:', response.data)
            
          } catch (err) {
            const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred'
            console.error('🟥 Batch emoji error:', errorMessage)
          } finally {
            setGenerating(false)
            await refresh()
          }
        }}
        disabled={generating}
        className="block mx-auto text-xs text-gray-500 hover:underline disabled:opacity-50"
      >
        {generating ? 'Generating…' : 'Generate & Refresh (last 15)'}
      </button>
    </div>
  )
} 