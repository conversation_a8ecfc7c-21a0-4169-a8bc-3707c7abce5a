'use client'

import React, { useState, useRef, useEffect } from 'react'
import { Settings } from 'lucide-react'
import { useSupabaseAuth } from '@/components/AuthProvider'
import { useTab } from '@/components/TabProvider'

export default function HeaderNav() {
  const { session, signInWithGoogle, signOut } = useSupabaseAuth()
  const { activeTab, setActiveTab } = useTab()
  const [showSettings, setShowSettings] = useState(false)
  const settingsRef = useRef<HTMLDivElement>(null)

  // Close settings menu when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (settingsRef.current && !settingsRef.current.contains(event.target as Node)) {
        setShowSettings(false)
      }
    }
    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const navItems = [
    { id: 'record' as const, name: 'RECORD', icon: '🎙️' },
    { id: 'history' as const, name: 'HISTORY', icon: '📜' },
    { id: 'insight' as const, name: 'INSIGHT', icon: '📚' },
  ]

  return (
    <header className="w-full bg-white h-16 min-h-[64px] flex items-center justify-between px-4 md:px-6 font-sans select-none shadow-sm">
      {/* Left: Logo */}
      <div className="flex items-center text-orange-500 text-2xl font-bold">
        Sentari
      </div>

      {/* Center: Navigation Tabs */}
      <nav className="flex-1 overflow-x-auto no-scrollbar">
        <div className="flex items-center gap-2 mx-auto justify-center flex-nowrap min-w-max">
          {navItems.map((item) => (
            <button
              key={item.id}
              onClick={() => setActiveTab(item.id)}
              className={`flex items-center gap-1 sm:gap-2 px-3 sm:px-4 py-1.5 text-xs sm:text-sm font-semibold uppercase tracking-wide rounded-full transition-all whitespace-nowrap
                ${activeTab === item.id
                  ? 'bg-orange-50 text-orange-500 shadow-sm'
                  : 'text-gray-800 bg-transparent hover:bg-gray-50'}
              `}
              style={{ fontFamily: 'inherit' }}
            >
              <span className="text-lg" style={{ fontFamily: 'inherit' }}>{item.icon}</span>
              <span>{item.name}</span>
            </button>
          ))}
        </div>
      </nav>

      {/* Right: Settings Menu */}
      <div className="relative" ref={settingsRef}>
        <button
          onClick={() => setShowSettings(!showSettings)}
          className={`p-2 rounded-full transition-all ${
            showSettings ? 'bg-gray-100 text-orange-500' : 'text-gray-600 hover:bg-gray-50'
          }`}
        >
          <Settings className="w-5 h-5" />
        </button>

        {/* Settings Dropdown */}
        {showSettings && (
          <div className="absolute right-0 mt-2 w-48 bg-white rounded-xl shadow-lg py-1 z-10">
            {session ? (
              <>
                <div className="px-4 py-2 border-b border-gray-100">
                  <div className="text-sm font-medium text-gray-700">{session.user?.email}</div>
                </div>
                <button
                  onClick={() => {
                    signOut()
                    setShowSettings(false)
                  }}
                  className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                >
                  Sign Out
                </button>
              </>
            ) : (
              <button
                onClick={() => {
                  signInWithGoogle()
                  setShowSettings(false)
                }}
                className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
              >
                Sign In
              </button>
            )}
          </div>
        )}
      </div>
    </header>
  )
}
