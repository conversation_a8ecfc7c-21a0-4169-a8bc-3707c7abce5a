import { Profile } from './types';

export interface EntryRecord {
  id: string;
  userId: string;
  rawText: string;
  createdAt: number;
  parsed: unknown;
  meta: unknown;
  embedding: number[];
}

const profiles = new Map<string, Profile>();
const entries: EntryRecord[] = [];

export function getProfile(userId: string): Profile | undefined {
  return profiles.get(userId);
}

export function saveProfile(userId: string, profile: Profile): void {
  profiles.set(userId, profile);
}

export function getRecentEntries(userId: string, limit = 5): EntryRecord[] {
  return entries.filter(e => e.userId === userId).slice(-limit);
}

export function saveEntry(record: EntryRecord): void {
  entries.push(record);
}

export function preloadEntries(userId: string, fakeEntries: EntryRecord[]): void {
  fakeEntries.forEach(e => entries.push(e));
  if (!profiles.has(userId)) {
    profiles.set(userId, {
      top_themes: [],
      theme_count: {},
      dominant_vibe: 'driven',
      vibe_count: { driven: 1 },
      bucket_count: {},
      trait_pool: []
    });
  }
}

export function clearDB(): void {
  profiles.clear();
  entries.length = 0;
} 