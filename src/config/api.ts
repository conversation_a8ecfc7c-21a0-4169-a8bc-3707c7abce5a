// API Configuration for Flask Backend



export const API_CONFIG = {
  // Base URL for Flask backend
  BASE_URL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000',
  
  // API endpoints
  ENDPOINTS: {
    ANALYZE: '/api/analyze',
    SAVE_ENTRY: '/api/save-entry',
    EMOTION_TREND: '/api/emotion-trend',
    PICK_EMOJI: '/api/pick-emoji',
    PICK_EMOJI_BATCH: '/api/pick-emoji-batch',
    RUN_PIPELINE: '/api/run',
    EMPATHY: '/api/empathy',
    UPDATE_TAGS: '/api/update-tags',
    UPDATE_TRANSCRIPT: '/api/update-transcript',
    WHISPER: '/api/whisper',
    TEST_OPENAI: '/api/test-openai',
    TEST_TAGS: '/api/test-tags'
  }
}

// Debug API config
console.log('🔍 API Config Debug:')
console.log('  BASE_URL:', API_CONFIG.BASE_URL)

// Helper function to get full API URL
export const getApiUrl = (endpoint: string): string => {
  return `${API_CONFIG.BASE_URL}${endpoint}`
}

// Helper function to get auth headers
export const getAuthHeaders = (token?: string): Record<string, string> => {
  const headers: Record<string, string> = {
    'Content-Type': 'application/json'
  }
  
  if (token) {
    headers['Authorization'] = `Bearer ${token}`
  }
  
  return headers
} 