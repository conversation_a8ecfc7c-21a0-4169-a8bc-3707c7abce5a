'use client'

import { useState, useEffect, useCallback } from 'react'
import { getEmotionTrend, ApiResponse, EmotionTrendResponse } from '@/lib/api/apiService'

export interface TrendPoint {
  timestamp: string // ISO datetime string
  score: number
}

export function useEmotionTrend() {
  const [data, setData] = useState<TrendPoint[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchTrend = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)
      
      const response: ApiResponse<EmotionTrendResponse> = await getEmotionTrend()
      
      if (!response.success) {
        throw new Error(response.error || 'Failed to fetch trend')
      }
      
      setData(response.data?.trend || [])
    } catch (e: any) {
      const errorMessage = e instanceof Error ? e.message : 'Unknown error occurred'
      console.error('🟥 Emotion trend error:', errorMessage)
      setError(errorMessage)
    } finally {
      setLoading(false)
    }
  }, [])

  useEffect(() => {
    fetchTrend()
  }, [fetchTrend])

  return { data, loading, error, fetchTrend }
} 