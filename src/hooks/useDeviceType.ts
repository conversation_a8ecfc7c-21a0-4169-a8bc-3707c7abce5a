export type DeviceType = 'mobile' | 'tablet' | 'desktop'

// useDeviceType detects the current device category via viewport width.
// It listens to window resize events and updates accordingly.
// This hook contains no UI logic in line with project rules.
import { useEffect, useState } from 'react'

export function useDeviceType(): DeviceType {
  const getType = (): DeviceType => {
    if (typeof window === 'undefined') return 'desktop' // default during SSR
    const w = window.innerWidth
    if (w < 640) return 'mobile'
    if (w < 1024) return 'tablet'
    return 'desktop'
  }

  const [type, setType] = useState<DeviceType>(getType)

  useEffect(() => {
    const handle = () => setType(getType())
    window.addEventListener('resize', handle)
    return () => window.removeEventListener('resize', handle)
  }, [])

  return type
} 