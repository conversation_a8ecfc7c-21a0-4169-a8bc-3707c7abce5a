import { useCallback, useEffect, useRef, useState } from 'react'
import { recordingService } from '@/lib/recording/recordingService'
import { useSupabaseAuth } from '@/components/AuthProvider'
import {
  transcribeAudio,
  saveEntry,
  analyzeTags as apiAnalyzeTags,
  updateTranscript as apiUpdateTranscript,
  updateTags as apiUpdateTags,
} from '@/lib/api/entries'
import { getLocalTimestamp } from '@/lib/utils/timeLocal'

export type SaveResponse = any

export function useRecordingFlow() {
  const { session } = useSupabaseAuth()

  /* -------------------- recording status -------------------- */
  const [isRecording, setIsRecording] = useState(false)
  const recordingTimer = useRef<NodeJS.Timeout | null>(null)
  const [recordingDuration, setRecordingDuration] = useState(0)
  const MAX_SECONDS = 180 // 3 minutes per recording limit

  /* -------------------- transcript -------------------- */
  const [transcript, setTranscript] = useState('')
  const [editableTranscript, setEditableTranscript] = useState('')
  const [isEditingTranscript, setIsEditingTranscript] = useState(false)
  const [transcriptSaveStatus, setTranscriptSaveStatus] = useState<'idle' | 'saving' | 'saved' | 'error'>('idle')

  /* -------------------- tags -------------------- */
  const [editableTags, setEditableTags] = useState<string[]>([])
  const [isEditingTags, setIsEditingTags] = useState(false)
  const [tagsSaveStatus, setTagsSaveStatus] = useState<'idle' | 'saving' | 'saved' | 'error'>('idle')
  const [newTagInput, setNewTagInput] = useState('')
  const [showSuggestions, setShowSuggestions] = useState(false)
  const [filteredSuggestions, setFilteredSuggestions] = useState<string[]>([])

  /* -------------------- responses -------------------- */
  const [apiResponse, setApiResponse] = useState<any>(null)
  const [saveResponse, setSaveResponse] = useState<SaveResponse | null>(null)

  /* -------------------- audio -------------------- */
  const [audioBlob, setAudioBlob] = useState<Blob | null>(null)

  /** PREDEFINED TAGS (ideally should come from config) */
  const PREDEFINED_TAGS = [
    'happy',
    'sad',
    'anxious',
    'angry',
    'ashamed',
    'calm',
    'overwhelmed',
    'confused',
    'reflection',
    'impulse',
    'conversation',
    'decision',
    'escape',
    'planning',
    'family',
    'partner',
    'friend',
    'work',
    'loneliness',
    'social_fear',
    'wish',
    'goal',
    'doubt',
    'value_conflict',
    'acceptance',
    'helpless',
    'hopeful',
    'self_blame',
    'defensive',
    'gratitude',
    'indecisive',
    'denial',
    'control',
    'resilience',
    'rumination',
    'cognitive_bias',
    'meta_thinking',
    'distorted_thinking',
    'pattern',
    'curiosity',
  ]

  /* -------------------- helpers -------------------- */
  const startRecording = useCallback(async () => {
    try {
      await recordingService.start()
      setIsRecording(true)
      setRecordingDuration(0)
      recordingTimer.current = setInterval(() => {
        setRecordingDuration((d) => {
          // Auto-stop if max duration reached
          if (d + 1 >= MAX_SECONDS) {
            stopRecording()
          }
          return d + 1
        })
      }, 1000)
    } catch (err: any) {
      console.error('Failed to start recording', err)
      const msg = err instanceof Error ? err.message : 'Unknown recording error'
      // Update debug element if present
      const el = document.getElementById('debug-log')
      if (el) el.textContent = msg
      alert(msg)
    }
  }, [])

  const stopRecording = useCallback(async () => {
    try {
      const { blob, duration } = await recordingService.stop()
      setIsRecording(false)
      if (recordingTimer.current) clearInterval(recordingTimer.current)

      setAudioBlob(blob)

      // Store final duration (ensure at least 1 second)
      const finalDuration = Math.max(1, duration)

      // Reject too-short recordings (<1s)
      if (finalDuration < 1) {
        alert('Recording too short. Please record at least 1 second.')
        return
      }

      // wait a tick to ensure data flushed
      setTimeout(() => {
        uploadAndTranscribe(blob, finalDuration)
      }, 100)
    } catch (err) {
      console.error('Failed to stop recording', err)
    }
  }, [])

  const uploadAndTranscribe = useCallback(async (blob: Blob, durationSeconds: number) => {
    const audioFile = new File([blob], 'recording.webm', { type: 'audio/webm' })
    const formData = new FormData()
    formData.append('file', audioFile)

    try {
      const data = await transcribeAudio(formData)
      if (!data) {
        throw new Error('No transcription data received')
      }
      
      setApiResponse(data)
      setTranscript(data.text || '')
      setEditableTranscript(data.text || '')

      // 🚫 Remove known garbage transcript returned by Whisper on silence
      if (data.text && data.text.includes('请不吝点赞')) {
        console.warn('Filtered garbage transcript phrase from Whisper')
        data.text = ''
        setTranscript('')
        setEditableTranscript('')
      }

      // Reject when no speech detected
      if (!data.text || data.text.trim().length === 0) {
        alert('No speech detected. Please try again.')
        return
      }

      await saveEntryToDb(data.text, data.language || 'unknown', durationSeconds)
    } catch (err) {
      console.error('Whisper error', err)
    }
  }, [])

  const saveEntryToDb = useCallback(async (rawTranscript: string, language: string, durationSeconds: number) => {
    try {
      // 1) Save entry first (without tags so it is persisted ASAP)
      const initialSave = await saveEntry({
        transcript_raw: rawTranscript,
        transcript_user: rawTranscript,
        language_detected: language,
        language_rendered: apiResponse?.language_rendered || apiResponse?.debug?.renderingLanguage || 'zh',
        tags_model: [],
        tags_user: [],
        category: 'mixed_language',
        client_timestamp: new Date().toISOString(),
        audio_duration: durationSeconds,
      })

      if (!initialSave?.entry?.[0]?.id) {
        throw new Error('Initial save failed - no entry ID returned')
      }

      const entryId = initialSave.entry[0].id

      // 2) Run GPT tag analysis after entry exists
      const analyzeResult = await apiAnalyzeTags(rawTranscript, entryId)
      console.log('Tag analysis result:', analyzeResult)

      const selectedTags = analyzeResult?.selectedTags && Array.isArray(analyzeResult.selectedTags)
        ? analyzeResult.selectedTags
        : []

      // 3) Update tags in DB if any
      if (selectedTags.length > 0) {
        await apiUpdateTags(entryId, selectedTags)
      }

      // Update local state
      setEditableTags(selectedTags)

      // Merge full response for downstream edits
      setSaveResponse({
        ...initialSave,
        analysis: {
          ...analyzeResult?.analysis,
          selectedTags,
        },
      })

      console.log('Entry saved then tags updated:', {
        entryId,
        selectedTags,
      })

    } catch (err) {
      console.error('Save entry failed', err)
      // Reset tags on error
      setEditableTags([])
      setSaveResponse(null)
    }
  }, [apiResponse])

  /* ---- editing transcript & tags ---- */
  const saveEditedTranscript = useCallback(async () => {
    if (!saveResponse?.entry?.[0]?.id) return

    setTranscriptSaveStatus('saving')
    try {
      const result = await apiUpdateTranscript(saveResponse.entry[0].id, editableTranscript.trim())
      if (result.success) {
        setTranscriptSaveStatus('saved')
        setTranscript(editableTranscript.trim())
        setTimeout(() => setTranscriptSaveStatus('idle'), 2000)
      } else {
        throw new Error(result.error || 'Failed')
      }
    } catch (err) {
      console.error(err)
      setTranscriptSaveStatus('error')
      setTimeout(() => setTranscriptSaveStatus('idle'), 3000)
    }
  }, [saveResponse, editableTranscript])

  const saveEditedTags = useCallback(async () => {
    if (!saveResponse?.entry?.[0]?.id) return

    setTagsSaveStatus('saving')
    try {
      const result = await apiUpdateTags(saveResponse.entry[0].id, editableTags)
      if (result.success) {
        setTagsSaveStatus('saved')
        setTimeout(() => setTagsSaveStatus('idle'), 2000)
      } else {
        throw new Error(result.error || 'Failed')
      }
    } catch (err) {
      console.error(err)
      setTagsSaveStatus('error')
      setTimeout(() => setTagsSaveStatus('idle'), 3000)
    }
  }, [saveResponse, editableTags])

  /* ---- tag helpers ---- */
  const handleTagInputChange = (value: string) => {
    const filtered = PREDEFINED_TAGS.filter(
      (tag) => tag.toLowerCase().includes(value.toLowerCase()) && !editableTags.includes(tag)
    ).slice(0, 5)

    setNewTagInput(value)
    setShowSuggestions(filtered.length > 0)
    setFilteredSuggestions(filtered)
  }

  const addNewTag = (tagToAdd?: string) => {
    const trimmedTag = (tagToAdd || newTagInput).trim().toLowerCase()
    if (trimmedTag && !editableTags.includes(trimmedTag)) {
      setEditableTags((prev) => [...prev, trimmedTag])
    }
    setNewTagInput('')
    setShowSuggestions(false)
  }

  const removeTag = (tagToRemove: string) => {
    setEditableTags((prev) => prev.filter((tag) => tag !== tagToRemove))
  }

  /* cleanup timer on unmount */
  useEffect(() => {
    return () => {
      if (recordingTimer.current) clearInterval(recordingTimer.current)
    }
  }, [])

  const resetTranscriptStatus = () => setTranscriptSaveStatus('idle')
  const resetTagsStatus = () => setTagsSaveStatus('idle')

  return {
    // recording
    isRecording,
    recordingDuration,
    startRecording,
    stopRecording,

    // transcript
    transcript,
    editableTranscript,
    setEditableTranscript,
    isEditingTranscript,
    setIsEditingTranscript,
    transcriptSaveStatus,
    saveEditedTranscript,

    // tags
    editableTags,
    setEditableTags,
    isEditingTags,
    setIsEditingTags,
    tagsSaveStatus,
    saveEditedTags,
    newTagInput,
    setNewTagInput,
    showSuggestions,
    filteredSuggestions,
    handleTagInputChange,
    addNewTag,
    removeTag,

    // audio
    audioBlob,
    apiResponse,

    // responses
    saveResponse,

    // status reset helpers
    resetTranscriptStatus,
    resetTagsStatus,
  }
} 