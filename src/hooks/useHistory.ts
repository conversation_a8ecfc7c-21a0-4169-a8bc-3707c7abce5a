'use client'

import { useCallback, useEffect, useState, useMemo } from 'react'
import { useSupabaseAuth } from '@/components/AuthProvider'
import { 
  getEntries, 
  searchEntries, 
  deleteEntry, 
  updateEntryTranscript, 
  updateEntryTags,
  type Entry 
} from '@/lib/api/apiService'

export interface EditStateItem {
  isEditingTranscript: boolean
  isEditingTags: boolean
  editableTranscript: string
  editableTags: string[]
  transcriptSaveStatus: 'idle' | 'saving' | 'saved' | 'error'
  tagsSaveStatus: 'idle' | 'saving' | 'saved' | 'error'
  newTagInput: string
  showSuggestions: boolean
  filteredSuggestions: string[]
}

export type EditState = Record<string, EditStateItem>

// Tag categories for styling
export const TAG_CATEGORIES = {
  emotion: [
    'happy', 'sad', 'anxious', 'angry', 'ashamed', 'calm', 'overwhelmed', 'confused',
    'acceptance', 'helpless', 'hopeful', 'self_blame', 'defensive', 'gratitude'
  ],
  reflection: [
    'reflection', 'meta_thinking', 'pattern', 'curiosity', 'cognitive_bias',
    'distorted_thinking', 'rumination', 'insight'
  ],
  idea: [
    'goal', 'planning', 'decision', 'wish', 'value', 'solution',
    'action', 'learning'
  ]
}

// Flat tag list for client-side autocomplete (no OpenAI or env dependencies)
const TAG_LIST: string[] = Object.values(TAG_CATEGORIES).flat()

// ------------ In-memory cache (per session) for query → results ------------
const searchCache: Map<string, Entry[] | null> = new Map()

// Business Logic Layer
class EntryManager {
  constructor(private userId: string) {}

  async deleteFromDatabase(entryId: string) {
    const response = await deleteEntry(entryId)
    if (!response.success) throw new Error(response.error || 'Failed to delete entry')
    return true
  }

  async fetchUserEntries() {
    const response = await getEntries()
    if (!response.success) throw new Error(response.error || 'Failed to fetch entries')
    return response.data || []
  }

  async searchEntries(query: string) {
    const trimmed = query.trim()
    if (!trimmed) return null

    // ✅ Tag detection (#happy or plain tag word)
    const tagCandidate = trimmed.startsWith('#') ? trimmed.slice(1).toLowerCase() : trimmed.toLowerCase()

    // Helper to fetch by tags
    const fetchByTag = async (): Promise<Entry[]> => {
      const response = await getEntries({ tags: [tagCandidate] })
      if (!response.success) throw new Error(response.error || 'Failed to fetch entries by tag')
      return response.data || []
    }

    // ------- 1. Cache check (still keep per-query) -------------------------
    if (searchCache.has(trimmed)) {
      return searchCache.get(trimmed) || null
    }

    // If the query seems to be an exact tag match, short-circuit to tag search
    if (TAG_LIST.includes(tagCandidate)) {
      try {
        const tagData = await fetchByTag()
        searchCache.set(trimmed, tagData)
        return tagData
      } catch (err) {
        console.error('[Search] tag filter failed:', err)
      }
    }

    // ------- 2. Vector search via API -------------------------------------
    let combined: Entry[] = []
    try {
      const response = await searchEntries(trimmed)
      if (!response.success) throw new Error(response.error || 'Failed to search entries')
      combined = response.data || []
    } catch (rpcErr: any) {
      console.warn('[Search] API search failed, fallback to tag search', rpcErr)
      // Fallback to tag search only
      try {
        const tagData = await fetchByTag()
        combined = tagData
      } catch (tagErr) {
        console.error('[Search] Tag fallback also failed:', tagErr)
      }
    }

    // ------- 3. Merge with tag matches (if any) ---------------------------
    try {
      const tagData = await fetchByTag()
      combined = [...combined, ...tagData]
    } catch (_) {/* ignore */}

    // Deduplicate by id
    const deduped: Entry[] = []
    const seen = new Set<string>()
    combined.forEach((e) => {
      // Handle entries with null IDs by using created_at as fallback
      const entryIdentifier = e.id || e.created_at
      if (!seen.has(entryIdentifier)) {
        deduped.push(e)
        seen.add(entryIdentifier)
      }
    })

    // Cache and return
    searchCache.set(trimmed, deduped)
    return deduped
  }
}

export function useHistory() {
  const { session } = useSupabaseAuth()
  const [entries, setEntries] = useState<Entry[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [editState, setEditStateInternal] = useState<EditState>({})
  const [searchQuery, setSearchQuery] = useState('')
  const [isSearching, setIsSearching] = useState(false)
  const [searchResults, setSearchResults] = useState<Entry[] | null>(null)

  const userId = session?.user?.id || null

  const entryManager = useMemo(() => {
    if (!userId) return null
    return new EntryManager(userId)
  }, [userId])

  // Filtered entries based on search
  const displayedEntries = useMemo(() => {
    if (!searchQuery.trim()) return entries
    return searchResults || entries
  }, [entries, searchResults, searchQuery])

  const handleSearch = async (query: string) => {
    if (!entryManager) return

    setSearchQuery(query)
    if (!query.trim()) {
      setSearchResults(null)
      return
    }

    setIsSearching(true)
    try {
      const results = await entryManager.searchEntries(query)
      setSearchResults(results || [])
      setError(null)
    } catch (err: any) {
      console.error('Search failed:', err)
      const msg = err?.message || err?.details || 'Unknown error'
      setError('Failed to search entries: ' + msg)
    } finally {
      setIsSearching(false)
    }
  }

  const updateEditState = (entryId: string, update: Partial<EditStateItem>) => {
    /* updateEditState debug removed */
    setEditStateInternal((prev: EditState) => {
      const existing = prev[entryId] || {
        isEditingTranscript: false,
        isEditingTags: false,
        editableTranscript: '',
        editableTags: [],
        transcriptSaveStatus: 'idle',
        tagsSaveStatus: 'idle',
        newTagInput: '',
        showSuggestions: false,
        filteredSuggestions: [],
      }

      return {
        ...prev,
        [entryId]: { ...existing, ...update },
      }
    })
  }

  const setEditState = (newState: EditState) => {
    setEditStateInternal(newState)
  }

  const deleteEntry = async (entryId: string) => {
    if (!entryManager) {
      return {
        success: false,
        error: 'User not authenticated'
      }
    }

    try {
      // Business logic: Delete from database
      await entryManager.deleteFromDatabase(entryId)

      // Only update UI state after successful deletion
      setEntries((prev) => prev.filter((e) => {
        // Handle entries with null IDs by using created_at as fallback
        const entryIdentifier = e.id || e.created_at
        return entryIdentifier !== entryId
      }))
      setEditStateInternal((prev: EditState) => {
        const newState = { ...prev }
        delete newState[entryId]
        return newState
      })

      return {
        success: true,
        error: null
      }
    } catch (err: any) {
      console.error('Delete failed:', err)
      return {
        success: false,
        error: err?.message || err?.details || 'Unknown error'
      }
    }
  }

  const loadEntries = useCallback(async () => {
    if (!entryManager) return

    setLoading(true)
    setError(null)
    try {
      console.log('🔄 Loading entries...')
      const data = await entryManager.fetchUserEntries()
      console.log('✅ Entries loaded:', data.length, 'entries')
      console.log('📝 Sample entry:', data[0])
      setEntries(data)
      
      // Initialize editState for all loaded entries
      const initialEditState: EditState = {}
      data.forEach((entry) => {
        const entryId = entry.id || entry.created_at
        initialEditState[entryId] = {
          isEditingTranscript: false,
          isEditingTags: false,
          editableTranscript: entry.transcript_user || '',
          editableTags: entry.tags_user || [],
          transcriptSaveStatus: 'idle',
          tagsSaveStatus: 'idle',
          newTagInput: '',
          showSuggestions: false,
          filteredSuggestions: [],
        }
      })
      console.log('🎯 EditState initialized for', Object.keys(initialEditState).length, 'entries')
      setEditStateInternal(initialEditState)
    } catch (err: any) {
      console.error('❌ Load failed:', err)
      const msg = err?.message || err?.details || 'Unknown error'
      setError('Failed to load entries: ' + msg)
    } finally {
      setLoading(false)
    }
  }, [entryManager])

  useEffect(() => {
    loadEntries()
  }, [loadEntries])

  const saveEditedTranscript = async (entryId: string) => {
    const editItem = editState[entryId]
    if (!editItem) return

    updateEditState(entryId, { transcriptSaveStatus: 'saving' })

    try {
      const response = await updateEntryTranscript(entryId, editItem.editableTranscript)
      if (!response.success) throw new Error(response.error || 'Failed to update transcript')

      setEntries((prev) =>
        prev.map((e) => {
          // Handle entries with null IDs by using created_at as fallback
          const entryIdentifier = e.id || e.created_at
          return entryIdentifier === entryId
            ? { ...e, transcript_user: editItem.editableTranscript }
            : e
        })
      )

      updateEditState(entryId, {
        transcriptSaveStatus: 'saved',
        isEditingTranscript: false,
      })
    } catch (err: any) {
      console.error('Save transcript failed:', err)
      updateEditState(entryId, {
        transcriptSaveStatus: 'error',
      })
    }
  }

  const saveEditedTags = async (entryId: string) => {
    const editItem = editState[entryId]
    if (!editItem) return

    updateEditState(entryId, { tagsSaveStatus: 'saving' })

    try {
      const response = await updateEntryTags(entryId, editItem.editableTags)
      if (!response.success) throw new Error(response.error || 'Failed to update tags')

      setEntries((prev) =>
        prev.map((e) => {
          // Handle entries with null IDs by using created_at as fallback
          const entryIdentifier = e.id || e.created_at
          return entryIdentifier === entryId ? { ...e, tags_user: editItem.editableTags } : e
        })
      )

      updateEditState(entryId, {
        tagsSaveStatus: 'saved',
        isEditingTags: false,
      })
    } catch (err: any) {
      console.error('Save tags failed:', err)
      updateEditState(entryId, {
        tagsSaveStatus: 'error',
      })
    }
  }

  const handleTagInputChange = (entryId: string, value: string) => {
    updateEditState(entryId, {
      newTagInput: value,
      showSuggestions: value.length > 0,
      filteredSuggestions: value.length > 0
        ? TAG_LIST.filter((tag) =>
            tag.toLowerCase().includes(value.toLowerCase())
          )
        : [],
    })
  }

  const addNewTag = (entryId: string, tagToAdd?: string) => {
    const editItem = editState[entryId]
    if (!editItem) return

    const tag = tagToAdd || editItem.newTagInput.trim()
    if (!tag) return

    const newTags = [...editItem.editableTags, tag]
    updateEditState(entryId, {
      editableTags: newTags,
      newTagInput: '',
      showSuggestions: false,
      filteredSuggestions: [],
    })
  }

  const removeTag = (entryId: string, tagToRemove: string) => {
    const editItem = editState[entryId]
    if (!editItem) return

    const newTags = editItem.editableTags.filter((tag) => tag !== tagToRemove)
    updateEditState(entryId, { editableTags: newTags })
  }

  return {
    entries: displayedEntries,
    loading,
    error,
    searchQuery,
    isSearching,
    editState,
    handleSearch,
    deleteEntry,
    updateEditState,
    setEditState,
    saveEditedTranscript,
    saveEditedTags,
    handleTagInputChange,
    addNewTag,
    removeTag,
    refresh: loadEntries,
  }
} 