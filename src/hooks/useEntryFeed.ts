import { useCallback, useEffect, useState } from 'react'
import { useSupabaseAuth } from '@/components/AuthProvider'
import { formatISO } from 'date-fns'
import { getEntries, getAvailableTags, type EntryFeedItem } from '@/lib/api/apiService'

export interface FeedFilters {
  tags: string[]
  startDate: string | null
  endDate: string | null
}

export function useEntryFeed(entriesPerPage = 10) {
  const { session } = useSupabaseAuth()

  const [entries, setEntries] = useState<EntryFeedItem[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [page, setPage] = useState(1)
  const [hasMore, setHasMore] = useState(true)

  const [filters, setFilters] = useState<FeedFilters>({ tags: [], startDate: null, endDate: null })
  const [availableTags, setAvailableTags] = useState<string[]>([])

  /* -------------------- fetch tags -------------------- */
  const fetchAvailableTags = useCallback(async () => {
    if (!session?.user?.id) return
    try {
      const response = await getAvailableTags()
      if (!response.success) throw new Error(response.error || 'Failed to fetch tags')
      
      setAvailableTags(response.data || [])
    } catch (err) {
      console.error('Fetch tags error', err)
    }
  }, [session])

  /* -------------------- fetch entries -------------------- */
  const fetchEntries = useCallback(async () => {
    if (!session?.user?.id) return

    try {
      setLoading(true)
      setError(null)

      const params: any = {
        limit: entriesPerPage,
        offset: (page - 1) * entriesPerPage
      }

      if (filters.tags.length > 0) {
        params.tags = filters.tags
      }
      if (filters.startDate) {
        params.start_date = filters.startDate
      }
      if (filters.endDate) {
        params.end_date = filters.endDate
      }

      const response = await getEntries(params)
      if (!response.success) throw new Error(response.error || 'Failed to load entries')

      const data = response.data || []
      setEntries((prev) => (page === 1 ? data : [...prev, ...data]))
      setHasMore(data.length === entriesPerPage)
    } catch (err) {
      console.error(err)
      setError('Failed to load entries')
    } finally {
      setLoading(false)
    }
  }, [session, page, filters, entriesPerPage])

  /* -------------------- initial load -------------------- */
  useEffect(() => {
    fetchAvailableTags()
  }, [fetchAvailableTags])

  useEffect(() => {
    fetchEntries()
  }, [fetchEntries])

  /* -------------------- handlers -------------------- */
  const toggleTagFilter = (tag: string) => {
    setFilters((prev) => {
      const next = prev.tags.includes(tag)
        ? prev.tags.filter((t) => t !== tag)
        : [...prev.tags, tag]
      return { ...prev, tags: next }
    })
    setPage(1)
  }

  const setDateFilter = (type: 'start' | 'end', date: string) => {
    setFilters((prev) => ({
      ...prev,
      [type === 'start' ? 'startDate' : 'endDate']: date ? formatISO(new Date(date)) : null,
    }))
    setPage(1)
  }

  const loadMore = () => {
    if (hasMore && !loading) setPage((p) => p + 1)
  }

  return {
    entries,
    loading,
    error,
    hasMore,
    filters,
    availableTags,
    toggleTagFilter,
    setDateFilter,
    loadMore,
  }
} 