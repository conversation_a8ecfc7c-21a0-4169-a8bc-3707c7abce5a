import { useState, useCallback } from 'react'
import { useSupabaseAuth } from '@/components/AuthProvider'
import { getEntry, updateEntryField } from '@/lib/api/apiService'

interface UseLazyComputeProps<TInput, TResult> {
  entryId: string
  resultField: string
  logField?: string
  computeFunction: (input: TInput) => Promise<TResult>
  inputPayload: TInput
  onSuccess?: (result: TResult) => void
  onError?: (error: Error) => void
}

interface UseLazyComputeResult<TResult> {
  triggerCompute: () => Promise<void>
  loading: boolean
  result: TResult | null
  error: Error | null
  isComputed: boolean
}

export function useLazyCompute<TInput, TResult>({
  entryId,
  resultField,
  logField,
  computeFunction,
  inputPayload,
  onSuccess,
  onError
}: UseLazyComputeProps<TInput, TResult>): UseLazyComputeResult<TResult> {
  const { session } = useSupabaseAuth()
  const [loading, setLoading] = useState(false)
  const [result, setResult] = useState<TResult | null>(null)
  const [error, setError] = useState<Error | null>(null)
  const [isComputed, setIsComputed] = useState(false)

  const checkExisting = useCallback(async (): Promise<TResult | null> => {
    if (!session?.user?.id || !entryId) return null

    try {
      const response = await getEntry(entryId)
      if (!response.success) throw new Error(response.error || 'Failed to check existing result')

      const data = response.data as any
      if (data && data[resultField] !== null) {
        setIsComputed(true)
        return data[resultField]
      }

      return null
    } catch (err) {
      console.error('Failed to check existing result:', err)
      return null
    }
  }, [session, entryId, resultField])

  const triggerCompute = useCallback(async () => {
    if (!session?.user?.id) {
      const authError = new Error('请先登录')
      setError(authError)
      onError?.(authError)
      return
    }

    if (!entryId) {
      const idError = new Error('Missing entry ID')
      setError(idError)
      onError?.(idError)
      return
    }

    try {
      setLoading(true)
      setError(null)

      // 1. Check for existing result
      const existingResult = await checkExisting()
      if (existingResult) {
        setResult(existingResult)
        onSuccess?.(existingResult)
        return
      }

      // 2. Run compute function
      console.log('🧮 Starting computation for:', {
        field: resultField,
        entryId
      })

      const computedResult = await computeFunction(inputPayload)

      // 3. Write to API
      const updatePayload: Record<string, any> = {
        [resultField]: computedResult
      }

      if (logField) {
        updatePayload[logField] = JSON.stringify({
          result: computedResult,
          timestamp: new Date().toISOString(),
          computedFor: entryId
        })
      }

      const response = await updateEntryField(entryId, resultField, computedResult)
      if (!response.success) throw new Error(response.error || 'Failed to save computation result')

      console.log('✅ Computation completed and saved:', {
        field: resultField,
        entryId
      })

      setResult(computedResult)
      setIsComputed(true)
      onSuccess?.(computedResult)

    } catch (err) {
      console.error('Computation failed:', err)
      const computeError = err instanceof Error ? err : new Error('Computation failed')
      setError(computeError)
      onError?.(computeError)
    } finally {
      setLoading(false)
    }
  }, [
    session,
    entryId,
    resultField,
    logField,
    computeFunction,
    inputPayload,
    checkExisting,
    onSuccess,
    onError
  ])

  return {
    triggerCompute,
    loading,
    result,
    error,
    isComputed
  }
} 