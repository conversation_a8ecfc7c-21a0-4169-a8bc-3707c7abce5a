// Purpose: Custom hook for emoji generation
// Process: Uses useLazyCompute to (1) check Supabase for existing entry_emoji, (2) if missing, POST to Flask backend /api/pick-emoji which performs GPT call and saves result.
// Output: emoji string, loading state, error state

import { useLazyCompute } from './useLazyCompute'
import { pickEmoji, ApiResponse, EmojiResponse } from '@/lib/api/apiService'

interface UseFunkyEmojiProps {
  entryId: string
  transcript?: string
  onSuccess?: (emoji: string) => void
  onError?: (error: Error) => void
}

export function useFunkyEmoji({ entryId, transcript, onSuccess, onError }: UseFunkyEmojiProps) {
  return useLazyCompute<string, string>({
    entryId,
    resultField: 'entry_emoji',
    logField: 'emoji_log',
    inputPayload: entryId, // we only need ID for compute
    onSuccess,
    onError,
    computeFunction: async (_unused) => {
      console.log('🎲 Generating funky emoji for entry:', entryId)
      
      const response: ApiResponse<EmojiResponse> = await pickEmoji(entryId, transcript)
      
      if (!response.success) {
        throw new Error(response.error || 'Failed to generate emoji')
      }
      
      if (response.data?.skipped) {
        console.log('🚫 Emoji generation skipped:', response.data)
      } else {
        console.log('😀 Funky emoji generated:', response.data?.emoji)
      }
      
      return response.data?.emoji || '😊'
    }
  })
} 