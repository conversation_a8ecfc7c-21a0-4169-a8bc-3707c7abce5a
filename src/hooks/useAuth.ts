import { useEffect, useState } from 'react'
import { Session } from '@supabase/supabase-js'
import { getSession, signInWithGoogle, signOut, supabaseAuthClient } from '@/lib/auth/authService'

export function useAuth() {
  const [session, setSession] = useState<Session | null>(null)
  const [initialized, setInitialized] = useState(false)

  useEffect(() => {
    async function init() {
      const current = await getSession()
      setSession(current)
      setInitialized(true)
    }
    init()

    const { data: listener } = supabaseAuthClient.auth.onAuthStateChange((_event: any, s: Session | null) => {
      setSession(s)
    })
    return () => {
      listener.subscription.unsubscribe()
    }
  }, [])

  return {
    session,
    loading: !initialized,
    signInWithGoogle,
    signOut,
    supabase: supabaseAuthClient,
  }
} 