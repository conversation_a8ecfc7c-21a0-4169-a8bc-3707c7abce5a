// Purpose: Custom hook for GPT-powered tag analysis
// Input: transcript text
// Process: Call Flask backend /api/analyze endpoint
// Output: tags, emotion score, confidence, loading state

import { useState } from 'react'
import { analyzeTags, ApiResponse, TagAnalysisResponse } from '@/lib/api/apiService'

export interface TagAnalysisResult {
  selectedTags: string[]
  confidence: Record<string, number>
  reasoning: string
  emotionScore: number
  categories: Record<string, string[]>
}

export interface UseTagAnalysisResult {
  analyzeText: (transcript: string, entryId?: string) => Promise<TagAnalysisResult | null>
  isAnalyzing: boolean
  error: string | null
  lastResult: TagAnalysisResult | null
}

export function useTagAnalysis(): UseTagAnalysisResult {
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [lastResult, setLastResult] = useState<TagAnalysisResult | null>(null)

  const analyzeText = async (
    transcript: string, 
    entryId?: string
  ): Promise<TagAnalysisResult | null> => {
    if (!transcript?.trim()) {
      setError('No transcript provided')
      return null
    }

    setIsAnalyzing(true)
    setError(null)

    try {
      console.log('🔍 Starting tag analysis...', {
        transcriptLength: transcript.length,
        hasEntryId: !!entryId
      })

      const response: ApiResponse<TagAnalysisResponse> = await analyzeTags(transcript, entryId)

      if (!response.success) {
        throw new Error(response.error || 'Analysis failed')
      }

      const data = response.data!
      
      // Transform the response to match the expected format
      const result: TagAnalysisResult = {
        selectedTags: data.selectedTags || [],
        confidence: { 
          overall: data.analysis?.confidence || 0
        },
        reasoning: `Purpose: ${data.analysis?.purpose}, Tone: ${data.analysis?.tone}, Category: ${data.analysis?.category}`,
        emotionScore: 0, // Not provided by Flask backend, could be enhanced
        categories: {
          purpose: data.analysis?.purpose ? [data.analysis.purpose] : [],
          tone: data.analysis?.tone ? [data.analysis.tone] : [],
          category: data.analysis?.category ? [data.analysis.category] : []
        }
      }

      setLastResult(result)
      console.log('✅ Tag analysis completed:', {
        tagsFound: result.selectedTags.length,
        confidence: data.analysis?.confidence
      })

      return result

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred'
      console.error('🟥 Tag analysis error:', errorMessage)
      setError(errorMessage)
      return null

    } finally {
      setIsAnalyzing(false)
    }
  }

  return {
    analyzeText,
    isAnalyzing,
    error,
    lastResult
  }
}

// Helper hook for batch analysis (future extension)
export function useBatchTagAnalysis() {
  const [isProcessing, setIsProcessing] = useState(false)
  const [progress, setProgress] = useState({ completed: 0, total: 0 })

  const analyzeBatch = async (transcripts: Array<{ id: string, text: string }>) => {
    setIsProcessing(true)
    setProgress({ completed: 0, total: transcripts.length })

    const results: Record<string, TagAnalysisResult | null> = {}

    for (let i = 0; i < transcripts.length; i++) {
      const { id, text } = transcripts[i]
      
      try {
        const response = await analyzeTags(text, id)
        
        if (response.success && response.data) {
          const data = response.data
          results[id] = {
            selectedTags: data.selectedTags || [],
            confidence: { 
              overall: data.analysis?.confidence || 0
            },
            reasoning: `Purpose: ${data.analysis?.purpose}, Tone: ${data.analysis?.tone}, Category: ${data.analysis?.category}`,
            emotionScore: 0,
            categories: {
              purpose: data.analysis?.purpose ? [data.analysis.purpose] : [],
              tone: data.analysis?.tone ? [data.analysis.tone] : [],
              category: data.analysis?.category ? [data.analysis.category] : []
            }
          }
        } else {
          results[id] = null
        }

      } catch (error) {
        console.error(`Failed to analyze ${id}:`, error)
        results[id] = null
      }

      setProgress({ completed: i + 1, total: transcripts.length })
    }

    setIsProcessing(false)
    return results
  }

  return {
    analyzeBatch,
    isProcessing,
    progress
  }
} 