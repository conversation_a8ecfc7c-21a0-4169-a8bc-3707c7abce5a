// Purpose: Fetch recent emoji entries (last 50) for fridge UI
// Input: limit (optional, default 50)
// Process: Query API for voice_entries where entry_emoji is not null, order by created_at desc, limited.
// Output: { entries, loading, error, refresh }

'use client'

import { useEffect, useState, useCallback } from 'react'
import { useSupabaseAuth } from '@/components/AuthProvider'
import { getRecentEmojiEntries, type EmojiEntry } from '@/lib/api/apiService'

// Export the EmojiEntry type for use in components
export type { EmojiEntry }

export function useRecentEmojiEntries(limit = 50) {
  const { session } = useSupabaseAuth()
  const [entries, setEntries] = useState<EmojiEntry[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<Error | null>(null)

  const fetchEntries = useCallback(async () => {
    if (!session?.user?.id) {
      setError(new Error('Unauthorized'))
      return
    }
    try {
      setLoading(true)
      setError(null)
      
      const response = await getRecentEmojiEntries(limit)
      if (!response.success) throw new Error(response.error || 'Failed to fetch emoji entries')

      setEntries(response.data || [])
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Unknown error'))
    } finally {
      setLoading(false)
    }
  }, [session, limit])

  useEffect(() => {
    fetchEntries()
  }, [fetchEntries])

  return { entries, loading, error, refresh: fetchEntries }
} 